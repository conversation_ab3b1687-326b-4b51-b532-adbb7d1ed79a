<template>
  <div class="application-detail">
    <!-- 页面标题 -->
    <div class="page-header">
      <el-button @click="$router.go(-1)" type="primary" plain>
        <el-icon><ArrowLeft /></el-icon>
        返回列表
      </el-button>
      <h2>准入申请详情 - {{ applicationInfo.applicationNo }}</h2>
    </div>

    <!-- 申请基本信息 -->
    <el-card class="info-card">
      <template #header>
        <div class="card-header">
          <span>申请基本信息</span>
          <el-tag :type="getStatusType(applicationInfo.status)">
            {{ getStatusText(applicationInfo.status) }}
          </el-tag>
        </div>
      </template>
      <el-row :gutter="20">
        <el-col :span="8">
          <div class="info-item">
            <label>申请编号：</label>
            <span>{{ applicationInfo.applicationNo }}</span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="info-item">
            <label>供应商名称：</label>
            <span>{{ applicationInfo.supplierName }}</span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="info-item">
            <label>申请时间：</label>
            <span>{{ applicationInfo.applicationDate }}</span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="info-item">
            <label>联系人：</label>
            <span>{{ applicationInfo.contactPerson }}</span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="info-item">
            <label>联系电话：</label>
            <span>{{ applicationInfo.phone }}</span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="info-item">
            <label>申请类型：</label>
            <span>{{ applicationInfo.applicationType }}</span>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 流程进度 -->
    <el-card class="info-card">
      <template #header>
        <span>流程进度</span>
      </template>
      <el-steps :active="currentStep" align-center>
        <el-step title="提交申请" description="供应商提交准入申请"></el-step>
        <el-step title="初步审核" description="文件审核和自评报告"></el-step>
        <el-step title="风险评估" description="财务、产能、技术评估"></el-step>
        <el-step title="现场审核" description="安排现场评审"></el-step>
        <el-step title="制样确认" description="制样确认跟踪"></el-step>
        <el-step title="准入决策" description="最终决策"></el-step>
      </el-steps>
    </el-card>

    <!-- 详细信息标签页 -->
    <el-card class="info-card">
      <el-tabs v-model="activeTab">
        <!-- 企业信息 -->
        <el-tab-pane label="企业信息" name="company">
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="info-item">
                <label>注册资本：</label>
                <span>{{ companyInfo.registeredCapital }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="info-item">
                <label>法定代表人：</label>
                <span>{{ companyInfo.legalRepresentative }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="info-item">
                <label>成立时间：</label>
                <span>{{ companyInfo.establishDate }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="info-item">
                <label>员工人数：</label>
                <span>{{ companyInfo.employeeCount }}</span>
              </div>
            </el-col>
            <el-col :span="24">
              <div class="info-item">
                <label>经营范围：</label>
                <span>{{ companyInfo.businessScope }}</span>
              </div>
            </el-col>
            <el-col :span="24">
              <div class="info-item">
                <label>公司地址：</label>
                <span>{{ companyInfo.address }}</span>
              </div>
            </el-col>
          </el-row>
        </el-tab-pane>

        <!-- 评估结果 -->
        <el-tab-pane label="评估结果" name="assessment">
          <el-table :data="assessmentResults" style="width: 100%">
            <el-table-column prop="category" label="评估类别" width="150" />
            <el-table-column prop="item" label="评估项目" min-width="200" />
            <el-table-column prop="score" label="得分" width="100">
              <template #default="scope">
                <el-tag :type="getScoreType(scope.row.score)">
                  {{ scope.row.score }}分
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="maxScore" label="满分" width="100" />
            <el-table-column prop="assessor" label="评估人" width="120" />
            <el-table-column prop="assessDate" label="评估时间" width="120" />
            <el-table-column prop="remark" label="备注" min-width="150" />
          </el-table>
        </el-tab-pane>

        <!-- 风险评估 -->
        <el-tab-pane label="风险评估" name="risk">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-card class="risk-card">
                <template #header>财务健康度</template>
                <div class="risk-content">
                  <div class="risk-score">
                    <span class="score-number">85</span>
                    <span class="score-label">分</span>
                  </div>
                  <el-progress :percentage="85" color="#67C23A" />
                  <div class="risk-detail">
                    <p>资产负债率：45%</p>
                    <p>流动比率：2.1</p>
                    <p>净利润率：12%</p>
                  </div>
                </div>
              </el-card>
            </el-col>
            <el-col :span="12">
              <el-card class="risk-card">
                <template #header>产能与技术</template>
                <div class="risk-content">
                  <div class="risk-score">
                    <span class="score-number">78</span>
                    <span class="score-label">分</span>
                  </div>
                  <el-progress :percentage="78" color="#409EFF" />
                  <div class="risk-detail">
                    <p>年产能：50万件</p>
                    <p>设备先进度：良好</p>
                    <p>技术团队：30人</p>
                  </div>
                </div>
              </el-card>
            </el-col>
            <el-col :span="12">
              <el-card class="risk-card">
                <template #header>质量控制</template>
                <div class="risk-content">
                  <div class="risk-score">
                    <span class="score-number">92</span>
                    <span class="score-label">分</span>
                  </div>
                  <el-progress :percentage="92" color="#67C23A" />
                  <div class="risk-detail">
                    <p>质量体系：ISO 9001</p>
                    <p>检测设备：齐全</p>
                    <p>质量人员：15人</p>
                  </div>
                </div>
              </el-card>
            </el-col>
            <el-col :span="12">
              <el-card class="risk-card">
                <template #header>合规性评估</template>
                <div class="risk-content">
                  <div class="risk-score">
                    <span class="score-number">88</span>
                    <span class="score-label">分</span>
                  </div>
                  <el-progress :percentage="88" color="#67C23A" />
                  <div class="risk-detail">
                    <p>环保合规：通过</p>
                    <p>安全生产：良好</p>
                    <p>社会责任：符合</p>
                  </div>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </el-tab-pane>

        <!-- 文档资料 -->
        <el-tab-pane label="文档资料" name="documents">
          <div class="document-header">
            <el-button type="primary" size="small">
              <el-icon><Upload /></el-icon>
              上传文档
            </el-button>
          </div>
          <el-table :data="documents" style="width: 100%">
            <el-table-column prop="name" label="文档名称" min-width="200" />
            <el-table-column prop="type" label="文档类型" width="120" />
            <el-table-column prop="size" label="文件大小" width="100" />
            <el-table-column prop="uploadDate" label="上传时间" width="120" />
            <el-table-column prop="uploader" label="上传人" width="100" />
            <el-table-column prop="status" label="审核状态" width="100">
              <template #default="scope">
                <el-tag :type="getDocStatusType(scope.row.status)">
                  {{ getDocStatusText(scope.row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="150">
              <template #default="scope">
                <el-button type="primary" size="small">预览</el-button>
                <el-button type="success" size="small">下载</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
      </el-tabs>
    </el-card>

    <!-- 操作按钮 -->
    <div class="action-footer">
      <el-button type="success" @click="handleApprove">
        <el-icon><Check /></el-icon>
        批准准入
      </el-button>
      <el-button type="danger" @click="handleReject">
        <el-icon><Close /></el-icon>
        拒绝准入
      </el-button>
      <el-button type="primary" @click="handleScheduleAudit">
        <el-icon><Calendar /></el-icon>
        安排现场审核
      </el-button>
      <el-button type="warning" @click="handleEdit">
        <el-icon><Edit /></el-icon>
        编辑申请
      </el-button>
    </div>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { useRoute } from 'vue-router'

export default {
  name: 'SupplierApplication',
  setup() {
    const route = useRoute()
    const activeTab = ref('company')
    const currentStep = ref(2)

    const applicationInfo = reactive({
      applicationNo: 'APP2024001',
      supplierName: '上海精密制造有限公司',
      applicationDate: '2024-01-15',
      contactPerson: '王经理',
      phone: '021-12345678',
      applicationType: '新供应商准入',
      status: 'reviewing'
    })

    const companyInfo = reactive({
      registeredCapital: '5000万人民币',
      legalRepresentative: '王建国',
      establishDate: '2010-03-15',
      employeeCount: '280人',
      businessScope: '汽车零部件制造、机械加工、模具设计制造',
      address: '上海市嘉定区工业园区创新路123号'
    })

    const assessmentResults = ref([
      {
        category: '财务评估',
        item: '资产负债率',
        score: 85,
        maxScore: 100,
        assessor: '财务部',
        assessDate: '2024-01-20',
        remark: '财务状况良好'
      },
      {
        category: '技术评估',
        item: '生产工艺',
        score: 78,
        maxScore: 100,
        assessor: '技术部',
        assessDate: '2024-01-22',
        remark: '工艺水平符合要求'
      },
      {
        category: '质量评估',
        item: '质量体系',
        score: 92,
        maxScore: 100,
        assessor: '质量部',
        assessDate: '2024-01-25',
        remark: '质量体系完善'
      },
      {
        category: '合规评估',
        item: '环保合规',
        score: 88,
        maxScore: 100,
        assessor: '合规部',
        assessDate: '2024-01-28',
        remark: '环保措施到位'
      }
    ])

    const documents = ref([
      {
        name: '营业执照',
        type: '资质证书',
        size: '2.5MB',
        uploadDate: '2024-01-15',
        uploader: '王经理',
        status: 'approved'
      },
      {
        name: 'ISO 9001证书',
        type: '认证证书',
        size: '1.8MB',
        uploadDate: '2024-01-16',
        uploader: '王经理',
        status: 'approved'
      },
      {
        name: '财务审计报告',
        type: '财务文档',
        size: '5.2MB',
        uploadDate: '2024-01-18',
        uploader: '王经理',
        status: 'pending'
      },
      {
        name: '产能证明文件',
        type: '技术文档',
        size: '3.1MB',
        uploadDate: '2024-01-20',
        uploader: '王经理',
        status: 'approved'
      }
    ])

    // 状态相关方法
    const getStatusType = (status) => {
      const types = {
        pending: 'info',
        reviewing: 'warning',
        auditing: 'primary',
        testing: 'success',
        approved: 'success',
        rejected: 'danger'
      }
      return types[status] || ''
    }

    const getStatusText = (status) => {
      const texts = {
        pending: '待审核',
        reviewing: '审核中',
        auditing: '现场审核',
        testing: '制样确认',
        approved: '已通过',
        rejected: '已拒绝'
      }
      return texts[status] || status
    }

    const getScoreType = (score) => {
      if (score >= 90) return 'success'
      if (score >= 80) return 'primary'
      if (score >= 70) return 'warning'
      return 'danger'
    }

    const getDocStatusType = (status) => {
      const types = {
        pending: 'warning',
        approved: 'success',
        rejected: 'danger'
      }
      return types[status] || ''
    }

    const getDocStatusText = (status) => {
      const texts = {
        pending: '待审核',
        approved: '已通过',
        rejected: '已拒绝'
      }
      return texts[status] || status
    }

    // 事件处理方法
    const handleApprove = () => {
      console.log('批准准入')
    }

    const handleReject = () => {
      console.log('拒绝准入')
    }

    const handleScheduleAudit = () => {
      console.log('安排现场审核')
    }

    const handleEdit = () => {
      console.log('编辑申请')
    }

    return {
      activeTab,
      currentStep,
      applicationInfo,
      companyInfo,
      assessmentResults,
      documents,
      getStatusType,
      getStatusText,
      getScoreType,
      getDocStatusType,
      getDocStatusText,
      handleApprove,
      handleReject,
      handleScheduleAudit,
      handleEdit
    }
  }
}
</script>

<style scoped>
.application-detail {
  padding: 0;
}

.page-header {
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 20px;
}

.page-header h2 {
  color: #303133;
  margin: 0;
}

.info-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.info-item {
  margin-bottom: 15px;
}

.info-item label {
  font-weight: bold;
  color: #606266;
  margin-right: 8px;
}

.risk-card {
  height: 100%;
}

.risk-content {
  text-align: center;
}

.risk-score {
  margin-bottom: 15px;
}

.score-number {
  font-size: 36px;
  font-weight: bold;
  color: #303133;
}

.score-label {
  font-size: 14px;
  color: #909399;
  margin-left: 4px;
}

.risk-detail {
  margin-top: 15px;
  text-align: left;
}

.risk-detail p {
  margin: 5px 0;
  font-size: 14px;
  color: #606266;
}

.document-header {
  margin-bottom: 15px;
  text-align: right;
}

.action-footer {
  margin-top: 30px;
  text-align: center;
  padding: 20px;
  border-top: 1px solid #e4e7ed;
}

.action-footer .el-button {
  margin: 0 10px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .action-footer .el-button {
    margin: 5px;
    width: calc(50% - 10px);
  }
}
</style>
