<template>
  <div class="improvement-plan">
    <!-- 页面标题 -->
    <div class="page-header">
      <el-button @click="$router.go(-1)" type="primary" plain>
        <el-icon><ArrowLeft /></el-icon>
        返回列表
      </el-button>
      <h2>改进计划 - {{ planInfo.planNo }}</h2>
    </div>

    <!-- 计划基本信息 -->
    <el-card class="info-card">
      <template #header>
        <div class="card-header">
          <span>计划基本信息</span>
          <el-tag :type="getStatusType(planInfo.status)">
            {{ getStatusText(planInfo.status) }}
          </el-tag>
        </div>
      </template>
      <el-row :gutter="20">
        <el-col :span="8">
          <div class="info-item">
            <label>计划编号：</label>
            <span>{{ planInfo.planNo }}</span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="info-item">
            <label>供应商名称：</label>
            <span>{{ planInfo.supplierName }}</span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="info-item">
            <label>问题领域：</label>
            <span>{{ planInfo.problemArea }}</span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="info-item">
            <label>当前得分：</label>
            <span :class="getScoreClass(planInfo.currentScore)">{{ planInfo.currentScore }}</span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="info-item">
            <label>目标得分：</label>
            <span :class="getScoreClass(planInfo.targetScore)">{{ planInfo.targetScore }}</span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="info-item">
            <label>计划进度：</label>
            <el-progress :percentage="planInfo.progress" />
          </div>
        </el-col>
        <el-col :span="8">
          <div class="info-item">
            <label>负责人：</label>
            <span>{{ planInfo.responsible }}</span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="info-item">
            <label>创建时间：</label>
            <span>{{ planInfo.createDate }}</span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="info-item">
            <label>截止时间：</label>
            <span>{{ planInfo.deadline }}</span>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 问题分析 -->
    <el-card class="info-card">
      <template #header>
        <span>问题分析</span>
      </template>
      <div class="problem-analysis">
        <h4>问题描述</h4>
        <p>{{ problemAnalysis.description }}</p>
        
        <h4>根本原因分析</h4>
        <p>{{ problemAnalysis.rootCause }}</p>
        
        <h4>影响评估</h4>
        <p>{{ problemAnalysis.impact }}</p>
        
        <h4>改进目标</h4>
        <p>{{ problemAnalysis.objective }}</p>
      </div>
    </el-card>

    <!-- 改进措施 -->
    <el-card class="info-card">
      <template #header>
        <div class="card-header">
          <span>改进措施</span>
          <el-button type="primary" size="small" @click="handleAddAction">
            <el-icon><Plus /></el-icon>
            添加措施
          </el-button>
        </div>
      </template>
      <el-table :data="improvementActions" style="width: 100%">
        <el-table-column type="expand">
          <template #default="props">
            <div class="expand-content">
              <p><strong>详细描述：</strong>{{ props.row.detailDescription }}</p>
              <p><strong>预期效果：</strong>{{ props.row.expectedResult }}</p>
              <p><strong>资源需求：</strong>{{ props.row.resourceRequirement }}</p>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="actionNo" label="措施编号" width="120" />
        <el-table-column prop="title" label="措施标题" min-width="200" />
        <el-table-column prop="responsible" label="负责人" width="100" />
        <el-table-column prop="startDate" label="开始时间" width="120" />
        <el-table-column prop="endDate" label="完成时间" width="120" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="getActionStatusType(scope.row.status)">
              {{ getActionStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="progress" label="进度" width="120">
          <template #default="scope">
            <el-progress :percentage="scope.row.progress" :stroke-width="6" />
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200">
          <template #default="scope">
            <el-button type="primary" size="small" @click="handleEditAction(scope.row)">
              编辑
            </el-button>
            <el-button type="success" size="small" @click="handleCompleteAction(scope.row)">
              完成
            </el-button>
            <el-button type="danger" size="small" @click="handleDeleteAction(scope.row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 跟踪记录 -->
    <el-card class="info-card">
      <template #header>
        <div class="card-header">
          <span>跟踪记录</span>
          <el-button type="success" size="small" @click="handleAddRecord">
            <el-icon><Plus /></el-icon>
            添加记录
          </el-button>
        </div>
      </template>
      <el-timeline>
        <el-timeline-item
          v-for="record in trackingRecords"
          :key="record.id"
          :timestamp="record.date"
          :type="getRecordType(record.type)"
        >
          <el-card>
            <h4>{{ record.title }}</h4>
            <p>{{ record.content }}</p>
            <div class="record-info">
              <span class="record-author">记录人：{{ record.author }}</span>
              <el-tag :type="getRecordTypeColor(record.type)" size="small">
                {{ record.type }}
              </el-tag>
            </div>
          </el-card>
        </el-timeline-item>
      </el-timeline>
    </el-card>

    <!-- 效果评估 -->
    <el-card class="info-card">
      <template #header>
        <span>效果评估</span>
      </template>
      <el-row :gutter="20">
        <el-col :span="12">
          <div class="evaluation-section">
            <h4>关键指标对比</h4>
            <el-table :data="kpiComparison" style="width: 100%">
              <el-table-column prop="indicator" label="指标名称" />
              <el-table-column prop="before" label="改进前" />
              <el-table-column prop="after" label="改进后" />
              <el-table-column prop="improvement" label="改进幅度">
                <template #default="scope">
                  <span :class="scope.row.improvement > 0 ? 'improvement-positive' : 'improvement-negative'">
                    {{ scope.row.improvement > 0 ? '+' : '' }}{{ scope.row.improvement }}%
                  </span>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="evaluation-section">
            <h4>改进效果总结</h4>
            <div class="summary-metrics">
              <div class="metric-item">
                <span class="metric-label">目标达成率：</span>
                <span class="metric-value">{{ evaluationSummary.achievementRate }}%</span>
              </div>
              <div class="metric-item">
                <span class="metric-label">整体改进幅度：</span>
                <span class="metric-value improvement-positive">+{{ evaluationSummary.overallImprovement }}%</span>
              </div>
              <div class="metric-item">
                <span class="metric-label">投入成本：</span>
                <span class="metric-value">¥{{ evaluationSummary.cost }}</span>
              </div>
              <div class="metric-item">
                <span class="metric-label">ROI：</span>
                <span class="metric-value improvement-positive">{{ evaluationSummary.roi }}%</span>
              </div>
            </div>
            
            <div class="evaluation-conclusion">
              <h5>改进结论</h5>
              <el-select v-model="improvementConclusion" placeholder="请选择改进结论" style="width: 100%">
                <el-option label="目标已达成，计划完成" value="completed" />
                <el-option label="部分达成，需继续改进" value="partial" />
                <el-option label="未达成，需重新制定计划" value="failed" />
              </el-select>
              <el-input
                v-model="conclusionNote"
                type="textarea"
                :rows="3"
                placeholder="请输入改进结论说明"
                style="margin-top: 10px"
              />
            </div>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 操作按钮 -->
    <div class="action-footer">
      <el-button type="success" @click="handleCompletePlan">
        <el-icon><Check /></el-icon>
        完成计划
      </el-button>
      <el-button type="warning" @click="handleSuspendPlan">
        <el-icon><VideoPause /></el-icon>
        暂停计划
      </el-button>
      <el-button type="primary" @click="handleEditPlan">
        <el-icon><Edit /></el-icon>
        编辑计划
      </el-button>
      <el-button type="info" @click="handleExportReport">
        <el-icon><Download /></el-icon>
        导出报告
      </el-button>
    </div>
  </div>
</template>

<script>
import { ref, reactive } from 'vue'

export default {
  name: 'ImprovementPlan',
  setup() {
    const improvementConclusion = ref('')
    const conclusionNote = ref('')

    const planInfo = reactive({
      planNo: 'IMP2024001',
      supplierName: '法雷奥汽车空调湖北有限公司',
      problemArea: '交付质量',
      currentScore: 75,
      targetScore: 85,
      progress: 60,
      responsible: '采购部-李经理',
      createDate: '2024-01-15',
      deadline: '2024-06-30',
      status: 'ongoing'
    })

    const problemAnalysis = reactive({
      description: '该供应商在交付质量方面存在问题，主要表现为交期达成率偏低（94%），订单完成率不稳定，影响生产计划的执行。',
      rootCause: '1. 生产计划管理不够精细，缺乏有效的产能预测；2. 与客户沟通不及时，订单变更响应慢；3. 供应链协调能力有待提升。',
      impact: '交付问题导致我司生产计划调整频繁，增加了库存成本和管理复杂度，同时影响了对下游客户的交付承诺。',
      objective: '通过系统性改进措施，将交期达成率提升至95%以上，订单完成率稳定在98%以上，建立更可靠的供应链合作关系。'
    })

    const improvementActions = ref([
      {
        id: 1,
        actionNo: 'ACT001',
        title: '建立精细化生产计划管理',
        responsible: '生产部经理',
        startDate: '2024-02-01',
        endDate: '2024-04-30',
        status: 'ongoing',
        progress: 70,
        detailDescription: '引入先进的生产计划管理系统，建立基于历史数据的产能预测模型，提高生产计划的准确性和可执行性。',
        expectedResult: '生产计划准确率提升至95%以上，减少计划变更频率50%。',
        resourceRequirement: '软件系统投入50万元，培训费用5万元，人员投入2人月。'
      },
      {
        id: 2,
        actionNo: 'ACT002',
        title: '优化客户沟通机制',
        responsible: '销售部主管',
        startDate: '2024-02-15',
        endDate: '2024-05-15',
        status: 'completed',
        progress: 100,
        detailDescription: '建立客户沟通标准流程，设立专门的客户服务团队，实现订单变更的快速响应。',
        expectedResult: '订单变更响应时间缩短至4小时内，客户满意度提升20%。',
        resourceRequirement: '人员培训费用3万元，系统改造费用10万元。'
      },
      {
        id: 3,
        actionNo: 'ACT003',
        title: '供应链协调能力提升',
        responsible: '采购部经理',
        startDate: '2024-03-01',
        endDate: '2024-06-30',
        status: 'pending',
        progress: 0,
        detailDescription: '建立供应商协同平台，实现供应链信息的实时共享，提高供应链整体协调效率。',
        expectedResult: '供应链响应速度提升30%，库存周转率提高15%。',
        resourceRequirement: '平台开发费用80万元，实施费用20万元。'
      }
    ])

    const trackingRecords = ref([
      {
        id: 1,
        date: '2024-03-01',
        title: '第一阶段进展汇报',
        content: '生产计划管理系统已完成需求分析和系统设计，预计3月底完成开发，4月开始试运行。客户沟通机制优化已完成，效果良好。',
        author: '李经理',
        type: '进展汇报'
      },
      {
        id: 2,
        date: '2024-02-15',
        title: '客户沟通机制优化完成',
        content: '已建立标准的客户沟通流程，培训了专门的客户服务团队，订单变更响应时间已缩短至平均3小时。',
        author: '销售部主管',
        type: '里程碑'
      },
      {
        id: 3,
        date: '2024-02-01',
        title: '改进计划正式启动',
        content: '改进计划正式启动，各项措施开始实施。已成立改进项目组，明确各项任务的责任人和时间节点。',
        author: '李经理',
        type: '计划启动'
      }
    ])

    const kpiComparison = ref([
      {
        indicator: '交期达成率',
        before: '94%',
        after: '96%',
        improvement: 2.1
      },
      {
        indicator: '订单完成率',
        before: '96%',
        after: '98%',
        improvement: 2.1
      },
      {
        indicator: '客户满意度',
        before: '85%',
        after: '90%',
        improvement: 5.9
      },
      {
        indicator: '计划变更频率',
        before: '15次/月',
        after: '8次/月',
        improvement: -46.7
      }
    ])

    const evaluationSummary = reactive({
      achievementRate: 80,
      overallImprovement: 15.2,
      cost: '168,000',
      roi: 245
    })

    // 状态相关方法
    const getStatusType = (status) => {
      const types = {
        ongoing: 'warning',
        completed: 'success',
        suspended: 'info',
        cancelled: 'danger'
      }
      return types[status] || ''
    }

    const getStatusText = (status) => {
      const texts = {
        ongoing: '进行中',
        completed: '已完成',
        suspended: '已暂停',
        cancelled: '已取消'
      }
      return texts[status] || status
    }

    const getScoreClass = (score) => {
      if (score >= 90) return 'score-excellent'
      if (score >= 80) return 'score-good'
      if (score >= 70) return 'score-average'
      return 'score-poor'
    }

    const getActionStatusType = (status) => {
      const types = {
        pending: 'info',
        ongoing: 'warning',
        completed: 'success',
        cancelled: 'danger'
      }
      return types[status] || ''
    }

    const getActionStatusText = (status) => {
      const texts = {
        pending: '待开始',
        ongoing: '进行中',
        completed: '已完成',
        cancelled: '已取消'
      }
      return texts[status] || status
    }

    const getRecordType = (type) => {
      const types = {
        '计划启动': 'primary',
        '进展汇报': 'success',
        '里程碑': 'warning',
        '问题记录': 'danger'
      }
      return types[type] || ''
    }

    const getRecordTypeColor = (type) => {
      const colors = {
        '计划启动': 'primary',
        '进展汇报': 'success',
        '里程碑': 'warning',
        '问题记录': 'danger'
      }
      return colors[type] || ''
    }

    // 事件处理方法
    const handleAddAction = () => {
      console.log('添加改进措施')
    }

    const handleEditAction = (row) => {
      console.log('编辑改进措施', row)
    }

    const handleCompleteAction = (row) => {
      row.status = 'completed'
      row.progress = 100
      console.log('完成改进措施', row)
    }

    const handleDeleteAction = (row) => {
      console.log('删除改进措施', row)
    }

    const handleAddRecord = () => {
      console.log('添加跟踪记录')
    }

    const handleCompletePlan = () => {
      console.log('完成改进计划')
    }

    const handleSuspendPlan = () => {
      console.log('暂停改进计划')
    }

    const handleEditPlan = () => {
      console.log('编辑改进计划')
    }

    const handleExportReport = () => {
      console.log('导出改进报告')
    }

    return {
      improvementConclusion,
      conclusionNote,
      planInfo,
      problemAnalysis,
      improvementActions,
      trackingRecords,
      kpiComparison,
      evaluationSummary,
      getStatusType,
      getStatusText,
      getScoreClass,
      getActionStatusType,
      getActionStatusText,
      getRecordType,
      getRecordTypeColor,
      handleAddAction,
      handleEditAction,
      handleCompleteAction,
      handleDeleteAction,
      handleAddRecord,
      handleCompletePlan,
      handleSuspendPlan,
      handleEditPlan,
      handleExportReport
    }
  }
}
</script>

<style scoped>
.improvement-plan {
  padding: 0;
}

.page-header {
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 20px;
}

.page-header h2 {
  color: #303133;
  margin: 0;
}

.info-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.info-item {
  margin-bottom: 15px;
}

.info-item label {
  font-weight: bold;
  color: #606266;
  margin-right: 8px;
}

.score-excellent {
  color: #67C23A;
  font-weight: bold;
}

.score-good {
  color: #409EFF;
  font-weight: bold;
}

.score-average {
  color: #E6A23C;
  font-weight: bold;
}

.score-poor {
  color: #F56C6C;
  font-weight: bold;
}

.problem-analysis h4 {
  color: #303133;
  margin: 20px 0 10px 0;
  font-size: 16px;
}

.problem-analysis h4:first-child {
  margin-top: 0;
}

.problem-analysis p {
  color: #606266;
  line-height: 1.6;
  margin-bottom: 15px;
}

.expand-content {
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 4px;
  margin: 10px 0;
}

.expand-content p {
  margin: 8px 0;
  line-height: 1.5;
}

.record-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 10px;
}

.record-author {
  color: #909399;
  font-size: 12px;
}

.evaluation-section {
  padding: 0 10px;
}

.evaluation-section h4 {
  margin-bottom: 15px;
  color: #303133;
}

.summary-metrics {
  margin-bottom: 20px;
}

.metric-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.metric-item:last-child {
  border-bottom: none;
}

.metric-label {
  color: #606266;
}

.metric-value {
  font-weight: bold;
  color: #303133;
}

.improvement-positive {
  color: #67C23A;
}

.improvement-negative {
  color: #F56C6C;
}

.evaluation-conclusion {
  margin-top: 20px;
}

.evaluation-conclusion h5 {
  margin-bottom: 10px;
  color: #303133;
}

.action-footer {
  margin-top: 30px;
  text-align: center;
  padding: 20px;
  border-top: 1px solid #e4e7ed;
}

.action-footer .el-button {
  margin: 0 10px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .record-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
  }

  .metric-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
  }

  .action-footer .el-button {
    margin: 5px;
    width: calc(50% - 10px);
  }
}
</style>
