<template>
  <div class="supplier-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>供应商档案管理</h2>
      <p>建立供应商信息的"单一真相来源"，确保数据完整、准确、及时更新</p>
    </div>

    <!-- 搜索和操作区域 -->
    <el-card class="search-card">
      <el-row :gutter="20">
        <el-col :span="5">
          <el-input
            v-model="searchForm.name"
            placeholder="请输入供应商名称"
            clearable
            @keyup.enter="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </el-col>
        <el-col :span="4">
          <el-select
            v-model="searchForm.status"
            placeholder="请选择状态"
            clearable
          >
            <el-option label="全部" value="" />
            <el-option label="正常" value="normal" />
            <el-option label="暂停" value="suspended" />
            <el-option label="黑名单" value="blacklist" />
          </el-select>
        </el-col>
        <el-col :span="5">
          <el-select
            v-model="searchForm.level"
            placeholder="请选择管理水平等级"
            clearable
          >
            <el-option label="全部" value="" />
            <el-option label="优秀" value="excellent" />
            <el-option label="良好" value="good" />
            <el-option label="一般" value="average" />
            <el-option label="需提升" value="improvement" />
          </el-select>
        </el-col>
        <el-col :span="4">
          <el-select
            v-model="searchForm.riskLevel"
            placeholder="风险标记"
            clearable
          >
            <el-option label="全部" value="" />
            <el-option label="正常" value="normal" />
            <el-option label="低风险" value="low" />
            <el-option label="中风险" value="medium" />
            <el-option label="高风险" value="high" />
          </el-select>
        </el-col>
        <el-col :span="3">
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
          <el-button type="success" @click="handleAdd">
            <el-icon><Plus /></el-icon>
            新增
          </el-button>
        </el-col>
      </el-row>
    </el-card>

    <!-- 数据表格 -->
    <el-card class="table-card">
      <el-table
        :data="tableData"
        style="width: 100%"
        v-loading="loading"
        row-key="id"
      >
        <el-table-column prop="code" label="供应商编码" width="120" />
        <el-table-column prop="name" label="供应商名称" min-width="200" />
        <el-table-column prop="contact" label="联系人" width="100" />
        <el-table-column prop="phone" label="联系电话" width="130" />
        <el-table-column prop="status" label="状态" width="80">
          <template #default="scope">
            <el-tag
              :type="getStatusType(scope.row.status)"
              size="small"
            >
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="level" label="管理水平等级" width="120">
          <template #default="scope">
            <el-tag
              :type="getLevelType(scope.row.level)"
              size="small"
            >
              {{ getLevelText(scope.row.level) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="partLevel" label="零件重要度" width="100">
          <template #default="scope">
            <el-tag
              :type="getPartLevelType(scope.row.partLevel)"
              size="small"
            >
              {{ scope.row.partLevel }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="riskLevel" label="风险标记" width="120">
          <template #default="scope">
            <div class="risk-indicator">
              <el-tag
                :type="getRiskLevelType(scope.row.riskLevel)"
                :class="getRiskLevelClass(scope.row.riskLevel)"
                size="small"
                effect="dark"
              >
                <el-icon class="risk-icon">
                  <component :is="getRiskIcon(scope.row.riskLevel)" />
                </el-icon>
                {{ getRiskLevelText(scope.row.riskLevel) }}
              </el-tag>
              <el-tooltip
                v-if="scope.row.riskFactors && scope.row.riskFactors.length > 0"
                :content="getRiskFactorsText(scope.row.riskFactors)"
                placement="top"
              >
                <el-icon class="risk-info-icon"><InfoFilled /></el-icon>
              </el-tooltip>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="certExpiry" label="认证到期时间" width="100" />
        <el-table-column label="操作" width="240" fixed="right">
          <template #default="scope">
            <div class="action-buttons">
              <el-button
                type="primary"
                size="small"
                @click="handleView(scope.row)"
              >
                查看
              </el-button>
              <el-button
                type="success"
                size="small"
                @click="handleEdit(scope.row)"
              >
                编辑
              </el-button>
              <el-button
                type="warning"
                size="small"
                @click="handleAudit(scope.row)"
              >
                审核
              </el-button>
              <el-button
                type="info"
                size="small"
                @click="handleDocs(scope.row)"
              >
                文档
              </el-button>
              <el-dropdown @command="handleCommand">
                <el-button type="primary" size="small">
                  更多<el-icon class="el-icon--right"><arrow-down /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item :command="{action: 'suspend', row: scope.row}">
                      暂停合作
                    </el-dropdown-item>
                    <el-dropdown-item :command="{action: 'blacklist', row: scope.row}">
                      加入黑名单
                    </el-dropdown-item>
                    <el-dropdown-item :command="{action: 'delete', row: scope.row}" divided>
                      删除
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 新增/编辑弹窗 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="600px"
      :before-close="handleDialogClose"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="120px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="供应商编码" prop="code">
              <el-input
                v-model="formData.code"
                placeholder="请输入供应商编码"
                :disabled="isEdit"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="供应商名称" prop="name">
              <el-input
                v-model="formData.name"
                placeholder="请输入供应商名称"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="联系人" prop="contact">
              <el-input
                v-model="formData.contact"
                placeholder="请输入联系人"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系电话" prop="phone">
              <el-input
                v-model="formData.phone"
                placeholder="请输入联系电话"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-select
                v-model="formData.status"
                placeholder="请选择状态"
                style="width: 100%"
              >
                <el-option label="正常" value="normal" />
                <el-option label="暂停" value="suspended" />
                <el-option label="黑名单" value="blacklist" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="管理水平等级" prop="level">
              <el-select
                v-model="formData.level"
                placeholder="请选择管理水平等级"
                style="width: 100%"
              >
                <el-option label="优秀" value="excellent" />
                <el-option label="良好" value="good" />
                <el-option label="一般" value="average" />
                <el-option label="需提升" value="improvement" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="零件重要度" prop="partLevel">
              <el-select
                v-model="formData.partLevel"
                placeholder="请选择零件重要度"
                style="width: 100%"
              >
                <el-option label="A级" value="A级" />
                <el-option label="B级" value="B级" />
                <el-option label="C级" value="C级" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="认证到期时间" prop="certExpiry">
              <el-date-picker
                v-model="formData.certExpiry"
                type="date"
                placeholder="请选择认证到期时间"
                style="width: 100%"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleDialogClose">取消</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="submitLoading">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'

export default {
  name: 'SupplierIndex',
  setup() {
    const router = useRouter()
    const loading = ref(false)
    const formRef = ref(null)
    const submitLoading = ref(false)

    // 弹窗相关
    const dialogVisible = ref(false)
    const isEdit = ref(false)
    const dialogTitle = ref('')
    
    // 搜索表单
    const searchForm = reactive({
      name: '',
      status: '',
      level: '',
      riskLevel: ''
    })

    // 分页信息
    const pagination = reactive({
      currentPage: 1,
      pageSize: 10,
      total: 0
    })

    // 表格数据
    const tableData = ref([])

    // 表单数据
    const formData = reactive({
      code: '',
      name: '',
      contact: '',
      phone: '',
      status: 'normal',
      level: 'good',
      partLevel: 'B级',
      certExpiry: ''
    })

    // 表单验证规则
    const formRules = {
      code: [
        { required: true, message: '请输入供应商编码', trigger: 'blur' },
        { min: 3, max: 20, message: '长度在 3 到 20 个字符', trigger: 'blur' }
      ],
      name: [
        { required: true, message: '请输入供应商名称', trigger: 'blur' },
        { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
      ],
      contact: [
        { required: true, message: '请输入联系人', trigger: 'blur' }
      ],
      phone: [
        { required: true, message: '请输入联系电话', trigger: 'blur' },
        { pattern: /^1[3-9]\d{9}$|^0\d{2,3}-?\d{7,8}$/, message: '请输入正确的电话号码', trigger: 'blur' }
      ],
      status: [
        { required: true, message: '请选择状态', trigger: 'change' }
      ],
      level: [
        { required: true, message: '请选择管理水平等级', trigger: 'change' }
      ],
      partLevel: [
        { required: true, message: '请选择零件重要度', trigger: 'change' }
      ],
      certExpiry: [
        { required: true, message: '请选择认证到期时间', trigger: 'change' }
      ]
    }

    // 初始化示例数据
    const initData = () => {
      tableData.value = [
        {
          id: 1,
          code: 'SUP001',
          name: '博世汽车部件(苏州)有限公司',
          contact: '张经理',
          phone: '0512-12345678',
          status: 'normal',
          level: 'excellent',
          partLevel: 'A级',
          certExpiry: '2024-12-31',
          riskLevel: 'normal',
          riskFactors: ['绩效评估优秀(92分)', '质量稳定', '交付及时'],
          performanceScore: 92,
          qualityScore: 95,
          deliveryScore: 90
        },
        {
          id: 2,
          code: 'SUP002',
          name: '大陆汽车系统(常熟)有限公司',
          contact: '李总监',
          phone: '0512-87654321',
          status: 'normal',
          level: 'good',
          partLevel: 'A级',
          certExpiry: '2024-10-15',
          riskLevel: 'low',
          riskFactors: ['绩效评估良好(86分)', '认证即将到期'],
          performanceScore: 86,
          qualityScore: 85,
          deliveryScore: 88
        },
        {
          id: 3,
          code: 'SUP003',
          name: '法雷奥汽车空调湖北有限公司',
          contact: '王主管',
          phone: '027-11223344',
          status: 'suspended',
          level: 'average',
          partLevel: 'B级',
          certExpiry: '2024-08-20',
          riskLevel: 'high',
          riskFactors: ['绩效评估不达标(65分)', '质量问题频发', '交付延迟'],
          performanceScore: 65,
          qualityScore: 68,
          deliveryScore: 62
        },
        {
          id: 4,
          code: 'SUP004',
          name: '麦格纳汽车技术(上海)有限公司',
          contact: '刘工程师',
          phone: '021-55667788',
          status: 'normal',
          level: 'excellent',
          partLevel: 'A级',
          certExpiry: '2025-03-10',
          riskLevel: 'normal',
          riskFactors: ['绩效评估卓越(95分)', '技术先进', '管理规范'],
          performanceScore: 95,
          qualityScore: 96,
          deliveryScore: 94
        },
        {
          id: 5,
          code: 'SUP005',
          name: '安波福电气系统有限公司',
          contact: '陈经理',
          phone: '0755-99887766',
          status: 'blacklist',
          level: 'improvement',
          partLevel: 'C级',
          certExpiry: '2024-06-30',
          riskLevel: 'high',
          riskFactors: ['绩效评估严重不达标(45分)', '黑名单状态', '质量严重问题'],
          performanceScore: 45,
          qualityScore: 42,
          deliveryScore: 48
        }
      ]
      pagination.total = tableData.value.length
    }

    // 状态相关方法
    const getStatusType = (status) => {
      const types = {
        normal: 'success',
        suspended: 'warning', 
        blacklist: 'danger'
      }
      return types[status] || ''
    }

    const getStatusText = (status) => {
      const texts = {
        normal: '正常',
        suspended: '暂停',
        blacklist: '黑名单'
      }
      return texts[status] || status
    }

    const getLevelType = (level) => {
      const types = {
        excellent: 'success',
        good: 'primary',
        average: 'warning',
        improvement: 'danger'
      }
      return types[level] || ''
    }

    const getLevelText = (level) => {
      const texts = {
        excellent: '优秀',
        good: '良好', 
        average: '一般',
        improvement: '需提升'
      }
      return texts[level] || level
    }

    const getPartLevelType = (partLevel) => {
      const types = {
        'A级': 'danger',
        'B级': 'warning',
        'C级': 'info'
      }
      return types[partLevel] || ''
    }

    // 风险标记相关方法 - 基于绩效评估数据
    const getRiskLevelType = (riskLevel) => {
      const types = {
        normal: 'success',
        low: 'info',
        medium: 'warning',
        high: 'danger'
      }
      return types[riskLevel] || 'info'
    }

    const getRiskLevelClass = (riskLevel) => {
      const classes = {
        normal: 'risk-normal',
        low: 'risk-low',
        medium: 'risk-medium',
        high: 'risk-high'
      }
      return classes[riskLevel] || ''
    }

    const getRiskLevelText = (riskLevel) => {
      const texts = {
        normal: '正常',
        low: '低风险',
        medium: '中风险',
        high: '高风险'
      }
      return texts[riskLevel] || riskLevel
    }

    const getRiskIcon = (riskLevel) => {
      const icons = {
        normal: 'CircleCheck',
        low: 'InfoFilled',
        medium: 'Warning',
        high: 'CircleClose'
      }
      return icons[riskLevel] || 'InfoFilled'
    }

    const getRiskFactorsText = (riskFactors) => {
      if (!riskFactors || riskFactors.length === 0) return ''
      return `风险因素：${riskFactors.join('、')}`
    }

    // 根据绩效评估计算风险等级
    const calculateRiskLevel = (performanceScore, qualityScore, deliveryScore, trend) => {
      // 基于绩效评估的风险计算逻辑
      if (performanceScore >= 90 && qualityScore >= 90 && deliveryScore >= 90) {
        return 'normal'
      } else if (performanceScore >= 80 || (qualityScore >= 80 && deliveryScore >= 80)) {
        return trend === 'down' ? 'medium' : 'low'
      } else if (performanceScore >= 70) {
        return 'medium'
      } else {
        return 'high'
      }
    }

    // 事件处理方法
    const handleSearch = () => {
      console.log('搜索', searchForm)
    }

    const handleReset = () => {
      Object.keys(searchForm).forEach(key => {
        searchForm[key] = ''
      })
    }

    const handleAdd = () => {
      isEdit.value = false
      dialogTitle.value = '新增供应商'
      resetForm()
      dialogVisible.value = true
    }

    const handleView = (row) => {
      router.push(`/supplier/detail/${row.id}`)
    }

    const handleEdit = (row) => {
      isEdit.value = true
      dialogTitle.value = '编辑供应商'
      // 填充表单数据
      Object.keys(formData).forEach(key => {
        if (row[key] !== undefined) {
          formData[key] = row[key]
        }
      })
      dialogVisible.value = true
    }

    const handleAudit = (row) => {
      console.log('审核', row)
    }

    const handleDocs = (row) => {
      console.log('文档管理', row)
    }

    const handleCommand = (command) => {
      console.log('执行操作', command)
    }

    const handleSizeChange = (val) => {
      pagination.pageSize = val
    }

    const handleCurrentChange = (val) => {
      pagination.currentPage = val
    }

    // 重置表单
    const resetForm = () => {
      Object.assign(formData, {
      code: '',
      name: '',
      contact: '',
      phone: '',
      status: 'normal',
      level: 'good',
      partLevel: 'B级',
      certExpiry: '',
      })
      if (formRef.value) {
        formRef.value.clearValidate()
      }
    }

    // 关闭弹窗
    const handleDialogClose = () => {
      dialogVisible.value = false
      resetForm()
    }

    // 提交表单
    const handleSubmit = async () => {
      if (!formRef.value) return

      try {
        await formRef.value.validate()
        submitLoading.value = true

        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 1000))

        if (isEdit.value) {
          // 编辑逻辑
          const index = tableData.value.findIndex(item => item.code === formData.code)
          if (index !== -1) {
            Object.assign(tableData.value[index], formData)
          }
          ElMessage.success('编辑成功')
        } else {
          // 新增逻辑
          const newId = Math.max(...tableData.value.map(item => item.id)) + 1
          tableData.value.unshift({
            id: newId,
            ...formData
          })
          pagination.total = tableData.value.length
          ElMessage.success('新增成功')
        }

        handleDialogClose()
      } catch (error) {
        console.error('表单验证失败:', error)
      } finally {
        submitLoading.value = false
      }
    }

    onMounted(() => {
      initData()
    })

    return {
      loading,
      searchForm,
      pagination,
      tableData,
      formRef,
      submitLoading,
      dialogVisible,
      isEdit,
      dialogTitle,
      formData,
      formRules,
      getStatusType,
      getStatusText,
      getLevelType,
      getLevelText,
      getPartLevelType,
      getRiskLevelType,
      getRiskLevelClass,
      getRiskLevelText,
      getRiskIcon,
      getRiskFactorsText,
      calculateRiskLevel,
      handleSearch,
      handleReset,
      handleAdd,
      handleView,
      handleEdit,
      handleAudit,
      handleDocs,
      handleCommand,
      handleSizeChange,
      handleCurrentChange,
      resetForm,
      handleDialogClose,
      handleSubmit
    }
  }
}
</script>

<style scoped>
.supplier-container {
  padding: 0;
  min-width: 1200px;
  overflow-x: auto;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  color: #303133;
  margin-bottom: 8px;
}

.page-header p {
  color: #909399;
  font-size: 14px;
}

.search-card {
  margin-bottom: 20px;
}

.table-card {
  margin-bottom: 20px;
}

.action-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  align-items: center;
}

.action-buttons .el-button {
  margin: 0;
  min-width: auto;
  padding: 5px 8px;
  font-size: 12px;
}

.action-buttons .el-dropdown {
  margin-left: 4px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .action-buttons .el-button {
    padding: 4px 6px;
    font-size: 11px;
  }
}

.pagination-container {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}

/* 弹窗样式 */
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.el-dialog__body {
  padding: 20px;
}

.el-form-item {
  margin-bottom: 18px;
}

.el-form-item__label {
  font-weight: 500;
  color: #606266;
}

/* 风险标记样式 */
.risk-indicator {
  display: flex;
  align-items: center;
  gap: 5px;
}

.risk-icon {
  margin-right: 2px;
}

.risk-info-icon {
  color: #909399;
  cursor: pointer;
  font-size: 14px;
}

.risk-info-icon:hover {
  color: #409EFF;
}

/* 风险等级特殊样式 - 基于绩效评估 */
.risk-normal {
  background: linear-gradient(135deg, #67C23A, #85CE61);
  border: none;
  color: white;
  font-weight: 500;
}

.risk-low {
  background: linear-gradient(135deg, #409EFF, #66B1FF);
  border: none;
  color: white;
  font-weight: 500;
}

.risk-medium {
  background: linear-gradient(135deg, #E6A23C, #EEBE77);
  border: none;
  color: white;
  font-weight: 500;
}

.risk-high {
  background: linear-gradient(135deg, #F56C6C, #F78989);
  border: none;
  color: white;
  font-weight: 500;
  animation: pulse-warning 2s infinite;
}

/* 风险警告动画 */
@keyframes pulse-warning {
  0% {
    box-shadow: 0 0 0 0 rgba(230, 162, 60, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(230, 162, 60, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(230, 162, 60, 0);
  }
}


</style>
