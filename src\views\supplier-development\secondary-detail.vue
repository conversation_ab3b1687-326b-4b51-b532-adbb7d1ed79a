<template>
  <div class="secondary-detail">
    <!-- 页面标题 -->
    <div class="page-header">
      <el-button @click="$router.go(-1)" type="primary" plain>
        <el-icon><ArrowLeft /></el-icon>
        返回列表
      </el-button>
      <h2>二供开发详情 - {{ projectInfo.projectNo }}</h2>
    </div>

    <!-- 项目基本信息 -->
    <el-card class="info-card">
      <template #header>
        <div class="card-header">
          <span>项目基本信息</span>
          <el-tag :type="getStatusType(projectInfo.status)">
            {{ getStatusText(projectInfo.status) }}
          </el-tag>
        </div>
      </template>
      <el-row :gutter="20">
        <el-col :span="8">
          <div class="info-item">
            <label>项目编号：</label>
            <span>{{ projectInfo.projectNo }}</span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="info-item">
            <label>物料编号：</label>
            <span>{{ projectInfo.materialCode }}</span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="info-item">
            <label>物料名称：</label>
            <span>{{ projectInfo.materialName }}</span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="info-item">
            <label>目标供应商：</label>
            <span>{{ projectInfo.targetSupplier }}</span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="info-item">
            <label>开发原因：</label>
            <span>{{ projectInfo.reason }}</span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="info-item">
            <label>项目经理：</label>
            <span>{{ projectInfo.projectManager }}</span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="info-item">
            <label>开始时间：</label>
            <span>{{ projectInfo.startDate }}</span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="info-item">
            <label>预计完成时间：</label>
            <span>{{ projectInfo.expectedEndDate }}</span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="info-item">
            <label>项目进度：</label>
            <el-progress :percentage="projectInfo.progress" />
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 开发计划 -->
    <el-card class="info-card">
      <template #header>
        <div class="card-header">
          <span>开发计划</span>
          <el-button type="primary" size="small">
            <el-icon><Plus /></el-icon>
            添加里程碑
          </el-button>
        </div>
      </template>
      <el-timeline>
        <el-timeline-item
          v-for="milestone in milestones"
          :key="milestone.id"
          :timestamp="milestone.plannedDate"
          :type="getMilestoneType(milestone.status)"
        >
          <el-card>
            <h4>{{ milestone.title }}</h4>
            <p>{{ milestone.description }}</p>
            <div class="milestone-info">
              <el-tag :type="getMilestoneStatusType(milestone.status)" size="small">
                {{ getMilestoneStatusText(milestone.status) }}
              </el-tag>
              <span class="milestone-owner">负责人：{{ milestone.owner }}</span>
            </div>
            <div v-if="milestone.actualDate" class="actual-date">
              实际完成时间：{{ milestone.actualDate }}
            </div>
          </el-card>
        </el-timeline-item>
      </el-timeline>
    </el-card>

    <!-- 风险评估 -->
    <el-card class="info-card">
      <template #header>
        <div class="card-header">
          <span>风险评估</span>
          <el-button type="warning" size="small">
            <el-icon><Plus /></el-icon>
            添加风险
          </el-button>
        </div>
      </template>
      <el-table :data="riskAssessment" style="width: 100%">
        <el-table-column prop="riskType" label="风险类型" width="120" />
        <el-table-column prop="description" label="风险描述" min-width="200" />
        <el-table-column prop="probability" label="发生概率" width="100">
          <template #default="scope">
            <el-tag :type="getProbabilityType(scope.row.probability)">
              {{ scope.row.probability }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="impact" label="影响程度" width="100">
          <template #default="scope">
            <el-tag :type="getImpactType(scope.row.impact)">
              {{ scope.row.impact }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="mitigation" label="缓解措施" min-width="200" />
        <el-table-column prop="owner" label="负责人" width="100" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="getRiskStatusType(scope.row.status)">
              {{ getRiskStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 历史问题回顾 -->
    <el-card class="info-card">
      <template #header>
        <span>历史问题回顾</span>
      </template>
      <el-table :data="historicalIssues" style="width: 100%">
        <el-table-column prop="issueDate" label="发生时间" width="120" />
        <el-table-column prop="issueType" label="问题类型" width="100" />
        <el-table-column prop="description" label="问题描述" min-width="200" />
        <el-table-column prop="rootCause" label="根本原因" min-width="150" />
        <el-table-column prop="correctionAction" label="纠正措施" min-width="150" />
        <el-table-column prop="preventiveAction" label="预防措施" min-width="150" />
        <el-table-column prop="verificationStatus" label="验证状态" width="100">
          <template #default="scope">
            <el-tag :type="getVerificationStatusType(scope.row.verificationStatus)">
              {{ getVerificationStatusText(scope.row.verificationStatus) }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 成本分析 -->
    <el-card class="info-card">
      <template #header>
        <span>成本分析</span>
      </template>
      <el-row :gutter="20">
        <el-col :span="12">
          <div class="cost-comparison">
            <h4>成本对比</h4>
            <el-table :data="costComparison" style="width: 100%">
              <el-table-column prop="item" label="项目" />
              <el-table-column prop="currentSupplier" label="现有供应商" />
              <el-table-column prop="targetSupplier" label="目标供应商" />
              <el-table-column prop="savings" label="节省金额">
                <template #default="scope">
                  <span :class="scope.row.savings > 0 ? 'cost-savings' : 'cost-increase'">
                    {{ scope.row.savings > 0 ? '+' : '' }}{{ scope.row.savings }}
                  </span>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="investment-analysis">
            <h4>投资分析</h4>
            <div class="investment-item">
              <span class="label">开发投入：</span>
              <span class="value">¥{{ investmentAnalysis.developmentCost }}</span>
            </div>
            <div class="investment-item">
              <span class="label">预期年节省：</span>
              <span class="value cost-savings">¥{{ investmentAnalysis.annualSavings }}</span>
            </div>
            <div class="investment-item">
              <span class="label">投资回收期：</span>
              <span class="value">{{ investmentAnalysis.paybackPeriod }}个月</span>
            </div>
            <div class="investment-item">
              <span class="label">ROI：</span>
              <span class="value cost-savings">{{ investmentAnalysis.roi }}%</span>
            </div>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 项目文档 -->
    <el-card class="info-card">
      <template #header>
        <div class="card-header">
          <span>项目文档</span>
          <el-button type="primary" size="small">
            <el-icon><Upload /></el-icon>
            上传文档
          </el-button>
        </div>
      </template>
      <el-table :data="projectDocuments" style="width: 100%">
        <el-table-column prop="name" label="文档名称" min-width="200" />
        <el-table-column prop="type" label="文档类型" width="120" />
        <el-table-column prop="version" label="版本" width="80" />
        <el-table-column prop="uploadDate" label="上传时间" width="120" />
        <el-table-column prop="uploader" label="上传人" width="100" />
        <el-table-column label="操作" width="150">
          <template #default="scope">
            <el-button type="primary" size="small">预览</el-button>
            <el-button type="success" size="small">下载</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 操作按钮 -->
    <div class="action-footer">
      <el-button type="success" @click="handleApproveProject">
        <el-icon><Check /></el-icon>
        批准项目
      </el-button>
      <el-button type="warning" @click="handleSuspendProject">
        <el-icon><VideoPause /></el-icon>
        暂停项目
      </el-button>
      <el-button type="primary" @click="handleEditProject">
        <el-icon><Edit /></el-icon>
        编辑项目
      </el-button>
      <el-button type="info" @click="handleExportReport">
        <el-icon><Download /></el-icon>
        导出报告
      </el-button>
    </div>
  </div>
</template>

<script>
import { ref, reactive } from 'vue'

export default {
  name: 'SecondaryDetail',
  setup() {
    const projectInfo = reactive({
      projectNo: 'SEC2024001',
      materialCode: 'MAT001',
      materialName: '制动盘总成',
      targetSupplier: '博世汽车部件(苏州)有限公司',
      reason: '单一来源风险',
      projectManager: '李项目经理',
      startDate: '2024-01-10',
      expectedEndDate: '2024-06-10',
      status: 'developing',
      progress: 60
    })

    const milestones = ref([
      {
        id: 1,
        title: '项目启动',
        description: '项目立项，组建项目团队',
        plannedDate: '2024-01-10',
        actualDate: '2024-01-10',
        status: 'completed',
        owner: '李项目经理'
      },
      {
        id: 2,
        title: '供应商评估',
        description: '对目标供应商进行全面评估',
        plannedDate: '2024-02-01',
        actualDate: '2024-02-05',
        status: 'completed',
        owner: '采购部'
      },
      {
        id: 3,
        title: '技术验证',
        description: '产品技术规格验证和样品测试',
        plannedDate: '2024-03-01',
        actualDate: '',
        status: 'ongoing',
        owner: '技术部'
      },
      {
        id: 4,
        title: '制样确认',
        description: '进行制样确认验证',
        plannedDate: '2024-04-01',
        actualDate: '',
        status: 'pending',
        owner: '质量部'
      },
      {
        id: 5,
        title: '正式导入',
        description: '正式导入二供，建立供应关系',
        plannedDate: '2024-06-01',
        actualDate: '',
        status: 'pending',
        owner: '采购部'
      }
    ])

    const riskAssessment = ref([
      {
        riskType: '技术风险',
        description: '新供应商技术能力不足',
        probability: '中',
        impact: '高',
        mitigation: '加强技术支持和培训',
        owner: '技术部',
        status: 'monitoring'
      },
      {
        riskType: '质量风险',
        description: '产品质量不稳定',
        probability: '低',
        impact: '高',
        mitigation: '严格质量控制和检验',
        owner: '质量部',
        status: 'controlled'
      },
      {
        riskType: '交付风险',
        description: '交付时间延迟',
        probability: '中',
        impact: '中',
        mitigation: '制定详细交付计划',
        owner: '采购部',
        status: 'monitoring'
      }
    ])

    const historicalIssues = ref([
      {
        issueDate: '2023-08-15',
        issueType: '质量问题',
        description: '产品尺寸超差',
        rootCause: '工艺参数设置不当',
        correctionAction: '调整工艺参数，重新加工',
        preventiveAction: '建立工艺参数标准化管理',
        verificationStatus: 'verified'
      },
      {
        issueDate: '2023-10-20',
        issueType: '交付问题',
        description: '交付延迟3天',
        rootCause: '生产计划安排不合理',
        correctionAction: '重新安排生产计划',
        preventiveAction: '优化生产计划管理系统',
        verificationStatus: 'verified'
      }
    ])

    const costComparison = ref([
      {
        item: '单价',
        currentSupplier: '¥120',
        targetSupplier: '¥110',
        savings: 10
      },
      {
        item: '年用量成本',
        currentSupplier: '¥1,200,000',
        targetSupplier: '¥1,100,000',
        savings: 100000
      },
      {
        item: '物流成本',
        currentSupplier: '¥50,000',
        targetSupplier: '¥60,000',
        savings: -10000
      }
    ])

    const investmentAnalysis = reactive({
      developmentCost: '150,000',
      annualSavings: '90,000',
      paybackPeriod: '20',
      roi: '60'
    })

    const projectDocuments = ref([
      {
        name: '二供开发计划书',
        type: '计划文档',
        version: 'V1.0',
        uploadDate: '2024-01-10',
        uploader: '李项目经理'
      },
      {
        name: '供应商评估报告',
        type: '评估报告',
        version: 'V2.0',
        uploadDate: '2024-02-05',
        uploader: '采购部'
      },
      {
        name: '技术规格书',
        type: '技术文档',
        version: 'V1.1',
        uploadDate: '2024-02-15',
        uploader: '技术部'
      }
    ])

    // 状态相关方法
    const getStatusType = (status) => {
      const types = {
        planning: 'info',
        developing: 'warning',
        validating: 'primary',
        completed: 'success',
        suspended: 'danger'
      }
      return types[status] || ''
    }

    const getStatusText = (status) => {
      const texts = {
        planning: '计划中',
        developing: '开发中',
        validating: '验证中',
        completed: '已完成',
        suspended: '已暂停'
      }
      return texts[status] || status
    }

    const getMilestoneType = (status) => {
      const types = {
        completed: 'success',
        ongoing: 'primary',
        pending: 'info',
        delayed: 'danger'
      }
      return types[status] || ''
    }

    const getMilestoneStatusType = (status) => {
      const types = {
        completed: 'success',
        ongoing: 'warning',
        pending: 'info',
        delayed: 'danger'
      }
      return types[status] || ''
    }

    const getMilestoneStatusText = (status) => {
      const texts = {
        completed: '已完成',
        ongoing: '进行中',
        pending: '待开始',
        delayed: '延期'
      }
      return texts[status] || status
    }

    const getProbabilityType = (probability) => {
      const types = {
        '低': 'success',
        '中': 'warning',
        '高': 'danger'
      }
      return types[probability] || ''
    }

    const getImpactType = (impact) => {
      const types = {
        '低': 'success',
        '中': 'warning',
        '高': 'danger'
      }
      return types[impact] || ''
    }

    const getRiskStatusType = (status) => {
      const types = {
        identified: 'warning',
        monitoring: 'primary',
        controlled: 'success',
        closed: 'info'
      }
      return types[status] || ''
    }

    const getRiskStatusText = (status) => {
      const texts = {
        identified: '已识别',
        monitoring: '监控中',
        controlled: '已控制',
        closed: '已关闭'
      }
      return texts[status] || status
    }

    const getVerificationStatusType = (status) => {
      const types = {
        pending: 'warning',
        verified: 'success',
        failed: 'danger'
      }
      return types[status] || ''
    }

    const getVerificationStatusText = (status) => {
      const texts = {
        pending: '待验证',
        verified: '已验证',
        failed: '验证失败'
      }
      return texts[status] || status
    }

    // 事件处理方法
    const handleApproveProject = () => {
      console.log('批准项目')
    }

    const handleSuspendProject = () => {
      console.log('暂停项目')
    }

    const handleEditProject = () => {
      console.log('编辑项目')
    }

    const handleExportReport = () => {
      console.log('导出报告')
    }

    return {
      projectInfo,
      milestones,
      riskAssessment,
      historicalIssues,
      costComparison,
      investmentAnalysis,
      projectDocuments,
      getStatusType,
      getStatusText,
      getMilestoneType,
      getMilestoneStatusType,
      getMilestoneStatusText,
      getProbabilityType,
      getImpactType,
      getRiskStatusType,
      getRiskStatusText,
      getVerificationStatusType,
      getVerificationStatusText,
      handleApproveProject,
      handleSuspendProject,
      handleEditProject,
      handleExportReport
    }
  }
}
</script>

<style scoped>
.secondary-detail {
  padding: 0;
}

.page-header {
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 20px;
}

.page-header h2 {
  color: #303133;
  margin: 0;
}

.info-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.info-item {
  margin-bottom: 15px;
}

.info-item label {
  font-weight: bold;
  color: #606266;
  margin-right: 8px;
}

.milestone-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 10px;
}

.milestone-owner {
  color: #909399;
  font-size: 12px;
}

.actual-date {
  margin-top: 8px;
  color: #67C23A;
  font-size: 12px;
}

.cost-comparison h4,
.investment-analysis h4 {
  margin-bottom: 15px;
  color: #303133;
}

.investment-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.investment-item:last-child {
  border-bottom: none;
}

.investment-item .label {
  color: #606266;
}

.investment-item .value {
  font-weight: bold;
  color: #303133;
}

.cost-savings {
  color: #67C23A;
}

.cost-increase {
  color: #F56C6C;
}

.action-footer {
  margin-top: 30px;
  text-align: center;
  padding: 20px;
  border-top: 1px solid #e4e7ed;
}

.action-footer .el-button {
  margin: 0 10px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .milestone-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
  }

  .action-footer .el-button {
    margin: 5px;
    width: calc(50% - 10px);
  }
}
</style>
