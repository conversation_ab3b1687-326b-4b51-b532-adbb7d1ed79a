import { createRouter, createWebHistory } from 'vue-router'
import Layout from '@/layout/index.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      component: Layout,
      redirect: '/supplier',
      children: [
        {
          path: '/supplier',
          name: 'Supplier',
          component: () => import('@/views/supplier/index.vue'),
          meta: { title: '供应商档案管理' }
        },
        {
          path: '/supplier/detail/:id',
          name: 'SupplierDetail',
          component: () => import('@/views/supplier/detail.vue'),
          meta: { title: '供应商详情' }
        },
        {
          path: '/supplier-development',
          name: 'SupplierDevelopment',
          component: () => import('@/views/supplier-development/index.vue'),
          meta: { title: '供应商开发与准入管理' }
        },
        {
          path: '/supplier-development/application/:id',
          name: 'SupplierApplication',
          component: () => import('@/views/supplier-development/application.vue'),
          meta: { title: '准入申请详情' }
        },
        {
          path: 'audit/:id',
          name: 'SupplierDevelopmentAudit',
          component: () => import('@/views/supplier-development/audit.vue'),
          meta: { title: '现场审核' }
        },
        {
          path: '/supplier-development/test-production/:id',
          name: 'TestProduction',
          component: () => import('@/views/supplier-development/test-production.vue'),
          meta: { title: '试生产跟踪' }
        },
        {
          path: '/supplier-development/secondary-detail/:id',
          name: 'SecondaryDetail',
          component: () => import('@/views/supplier-development/secondary-detail.vue'),
          meta: { title: '二供开发详情' }
        },
        {
          path: '/supplier-development/exit-detail/:id',
          name: 'ExitDetail',
          component: () => import('@/views/supplier-development/exit-detail.vue'),
          meta: { title: '供应商退出详情' }
        },
        {
          path: '/supplier-performance',
          name: 'SupplierPerformance',
          component: () => import('@/views/supplier-performance/index.vue'),
          meta: { title: '供应商绩效管理' }
        },
        {
          path: '/supplier-performance/dashboard/:id',
          name: 'PerformanceDashboard',
          component: () => import('@/views/supplier-performance/dashboard.vue'),
          meta: { title: '绩效仪表盘' }
        },
        {
          path: '/supplier-performance/improvement/:id',
          name: 'ImprovementPlan',
          component: () => import('@/views/supplier-performance/improvement.vue'),
          meta: { title: '改进计划' }
        },
        {
          path: '/supplier-audit',
          name: 'SupplierAudit',
          component: () => import('@/views/supplier-audit/index.vue'),
          meta: { title: '供应商审核管理' }
        },
        {
          path: '/supplier-audit/plan/create',
          name: 'CreateAuditPlan',
          component: () => import('@/views/supplier-audit/plan-create.vue'),
          meta: { title: '制定审核计划' }
        },
        {
          path: '/supplier-audit/plan/:id',
          name: 'ViewAuditPlan',
          component: () => import('@/views/supplier-audit/plan-detail.vue'),
          meta: { title: '审核计划详情' }
        },
        {
          path: '/supplier-audit/plan/edit/:id',
          name: 'EditAuditPlan',
          component: () => import('@/views/supplier-audit/plan-edit.vue'),
          meta: { title: '编辑审核计划' }
        },
        {
          path: '/supplier-audit/execution/:id',
          name: 'AuditExecution',
          component: () => import('@/views/supplier-audit/execution.vue'),
          meta: { title: '审核执行' }
        },
        {
          path: '/supplier-audit/report/:id',
          name: 'AuditReport',
          component: () => import('@/views/supplier-audit/report.vue'),
          meta: { title: '审核报告' }
        },
        {
          path: '/supplier-audit/capa/:id',
          name: 'CAPAManagement',
          component: () => import('@/views/supplier-audit/capa.vue'),
          meta: { title: 'CAPA管理' }
        },
        {
          path: '/supplier-audit/create',
          name: 'CreateAudit',
          component: () => import('@/views/supplier-audit/create.vue'),
          meta: { title: '新建审核' }
        }
      ]
    }
  ]
})

export default router
