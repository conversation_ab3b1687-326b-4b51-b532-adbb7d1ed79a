<template>
  <div class="supplier-audit-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <h2>供应商审核管理</h2>
      <div class="header-actions">
        <el-button type="primary" @click="handleCreateAudit">
          <el-icon><Plus /></el-icon>
          新建审核
        </el-button>
        <el-button type="success" @click="handleExportReport">
          <el-icon><Download /></el-icon>
          导出报告
        </el-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-number">{{ auditStats.total }}</div>
            <div class="stats-label">总审核数</div>
          </div>
          <el-icon class="stats-icon total"><Document /></el-icon>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-number">{{ auditStats.ongoing }}</div>
            <div class="stats-label">进行中</div>
          </div>
          <el-icon class="stats-icon ongoing"><Clock /></el-icon>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-number">{{ auditStats.completed }}</div>
            <div class="stats-label">已完成</div>
          </div>
          <el-icon class="stats-icon completed"><CircleCheck /></el-icon>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-number">{{ auditStats.overdue }}</div>
            <div class="stats-label">已逾期</div>
          </div>
          <el-icon class="stats-icon overdue"><Warning /></el-icon>
        </el-card>
      </el-col>
    </el-row>

    <!-- 主要内容 -->
    <el-card class="main-card">
      <el-tabs v-model="activeTab" @tab-change="handleTabChange">
        <!-- 审核计划 -->
        <el-tab-pane label="审核计划" name="plan">
          <div class="tab-header">
            <el-button type="primary" @click="handleCreatePlan">
              <el-icon><Plus /></el-icon>
              制定审核计划
            </el-button>
            <div class="filter-area">
              <el-select v-model="planSearchForm.year" placeholder="年度" style="width: 120px">
                <el-option label="2024年" value="2024" />
                <el-option label="2023年" value="2023" />
              </el-select>
              <el-select v-model="planSearchForm.type" placeholder="审核类型" style="width: 150px">
                <el-option label="全部类型" value="" />
                <el-option label="准入审核" value="onboarding" />
                <el-option label="年度审核" value="annual" />
                <el-option label="月度审核" value="monthly" />
                <el-option label="专项审核" value="special" />
                <el-option label="飞行检查" value="flying" />
              </el-select>
              <el-button type="primary" @click="handlePlanSearch">搜索</el-button>
            </div>
          </div>

          <el-table :data="auditPlans" style="width: 100%" v-loading="loading">
            <el-table-column prop="planNo" label="计划编号" width="150" />
            <el-table-column prop="year" label="年度" width="80" />
            <el-table-column prop="type" label="审核类型" width="120">
              <template #default="scope">
                <el-tag :type="getAuditTypeColor(scope.row.type)">
                  {{ getAuditTypeText(scope.row.type) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="supplierCount" label="供应商数量" width="120" />
            <el-table-column prop="plannedStart" label="计划开始" width="120" />
            <el-table-column prop="plannedEnd" label="计划结束" width="120" />
            <el-table-column prop="status" label="状态" width="100">
              <template #default="scope">
                <el-tag :type="getPlanStatusType(scope.row.status)">
                  {{ getPlanStatusText(scope.row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="progress" label="进度" width="120">
              <template #default="scope">
                <el-progress :percentage="scope.row.progress" :stroke-width="6" />
              </template>
            </el-table-column>
            <el-table-column label="操作" width="200">
              <template #default="scope">
                <el-button type="primary" size="small" @click="handleViewPlan(scope.row)">
                  查看
                </el-button>
                <el-button type="success" size="small" @click="handleEditPlan(scope.row)">
                  编辑
                </el-button>
                <el-button type="warning" size="small" @click="handleExecutePlan(scope.row)">
                  执行
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>

        <!-- 审核执行 -->
        <el-tab-pane label="审核执行" name="execution">
          <div class="tab-header">
            <div class="filter-area">
              <el-select v-model="executionSearchForm.status" placeholder="审核状态" style="width: 150px">
                <el-option label="全部状态" value="" />
                <el-option label="待开始" value="pending" />
                <el-option label="进行中" value="ongoing" />
                <el-option label="已完成" value="completed" />
                <el-option label="已逾期" value="overdue" />
              </el-select>
              <el-select v-model="executionSearchForm.type" placeholder="审核类型" style="width: 150px">
                <el-option label="全部类型" value="" />
                <el-option label="准入审核" value="onboarding" />
                <el-option label="年度审核" value="annual" />
                <el-option label="月度审核" value="monthly" />
                <el-option label="专项审核" value="special" />
                <el-option label="飞行检查" value="flying" />
              </el-select>
              <el-input
                v-model="executionSearchForm.keyword"
                placeholder="供应商名称"
                style="width: 200px"
              />
              <el-button type="primary" @click="handleExecutionSearch">搜索</el-button>
            </div>
          </div>

          <el-table :data="auditExecutions" style="width: 100%" v-loading="loading">
            <el-table-column prop="auditNo" label="审核编号" width="150" />
            <el-table-column prop="supplierName" label="供应商名称" min-width="200" />
            <el-table-column prop="type" label="审核类型" width="120">
              <template #default="scope">
                <el-tag :type="getAuditTypeColor(scope.row.type)">
                  {{ getAuditTypeText(scope.row.type) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="auditor" label="审核员" width="120" />
            <el-table-column prop="plannedDate" label="计划日期" width="120" />
            <el-table-column prop="actualDate" label="实际日期" width="120" />
            <el-table-column prop="status" label="状态" width="100">
              <template #default="scope">
                <el-tag :type="getExecutionStatusType(scope.row.status)">
                  {{ getExecutionStatusText(scope.row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="score" label="得分" width="80" />
            <el-table-column prop="riskLevel" label="风险标记" width="120">
              <template #default="scope">
                <div class="risk-indicator">
                  <el-tag
                    :type="getRiskLevelType(scope.row.riskLevel)"
                    :class="getRiskLevelClass(scope.row.riskLevel)"
                    size="small"
                    effect="dark"
                  >
                    <el-icon class="risk-icon">
                      <component :is="getRiskIcon(scope.row.riskLevel)" />
                    </el-icon>
                    {{ getRiskLevelText(scope.row.riskLevel) }}
                  </el-tag>
                  <el-tooltip
                    v-if="scope.row.riskFactors && scope.row.riskFactors.length > 0"
                    :content="getRiskFactorsText(scope.row.riskFactors)"
                    placement="top"
                  >
                    <el-icon class="risk-info-icon"><InfoFilled /></el-icon>
                  </el-tooltip>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="250">
              <template #default="scope">
                <el-button type="primary" size="small" @click="handleStartAudit(scope.row)">
                  开始审核
                </el-button>
                <el-button type="success" size="small" @click="handleViewReport(scope.row)">
                  查看报告
                </el-button>
                <el-button type="warning" size="small" @click="handleViewCAPA(scope.row)">
                  CAPA
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>

        <!-- CAPA管理 -->
        <el-tab-pane label="CAPA管理" name="capa">
          <div class="tab-header">
            <div class="filter-area">
              <el-select v-model="capaSearchForm.status" placeholder="CAPA状态" style="width: 150px">
                <el-option label="全部状态" value="" />
                <el-option label="待处理" value="pending" />
                <el-option label="进行中" value="ongoing" />
                <el-option label="待验证" value="verification" />
                <el-option label="已关闭" value="closed" />
                <el-option label="已逾期" value="overdue" />
              </el-select>
              <el-select v-model="capaSearchForm.priority" placeholder="优先级" style="width: 120px">
                <el-option label="全部" value="" />
                <el-option label="高" value="high" />
                <el-option label="中" value="medium" />
                <el-option label="低" value="low" />
              </el-select>
              <el-input
                v-model="capaSearchForm.keyword"
                placeholder="供应商名称"
                style="width: 200px"
              />
              <el-button type="primary" @click="handleCAPASearch">搜索</el-button>
            </div>
          </div>

          <el-table :data="capaList" style="width: 100%" v-loading="loading">
            <el-table-column prop="capaNo" label="CAPA编号" width="150" />
            <el-table-column prop="supplierName" label="供应商名称" min-width="200" />
            <el-table-column prop="auditNo" label="关联审核" width="150" />
            <el-table-column prop="nonConformity" label="不符合项" min-width="200" />
            <el-table-column prop="priority" label="优先级" width="100">
              <template #default="scope">
                <el-tag :type="getPriorityType(scope.row.priority)">
                  {{ scope.row.priority === 'high' ? '高' : scope.row.priority === 'medium' ? '中' : '低' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="dueDate" label="截止日期" width="120" />
            <el-table-column prop="status" label="状态" width="100">
              <template #default="scope">
                <el-tag :type="getCAPAStatusType(scope.row.status)">
                  {{ getCAPAStatusText(scope.row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="200">
              <template #default="scope">
                <el-button type="primary" size="small" @click="handleViewCAPA(scope.row)">
                  查看
                </el-button>
                <el-button type="success" size="small" @click="handleVerifyCAPA(scope.row)">
                  验证
                </el-button>
                <el-button type="warning" size="small" @click="handleCloseCAPA(scope.row)">
                  关闭
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>

        <!-- 审核报告 -->
        <el-tab-pane label="审核报告" name="report">
          <div class="tab-header">
            <div class="filter-area">
              <el-date-picker
                v-model="reportSearchForm.dateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                style="width: 240px"
              />
              <el-select v-model="reportSearchForm.type" placeholder="审核类型" style="width: 150px">
                <el-option label="全部类型" value="" />
                <el-option label="准入审核" value="onboarding" />
                <el-option label="年度审核" value="annual" />
                <el-option label="月度审核" value="monthly" />
                <el-option label="专项审核" value="special" />
                <el-option label="飞行检查" value="flying" />
              </el-select>
              <el-input
                v-model="reportSearchForm.keyword"
                placeholder="供应商名称"
                style="width: 200px"
              />
              <el-button type="primary" @click="handleReportSearch">搜索</el-button>
            </div>
          </div>

          <el-table :data="auditReports" style="width: 100%" v-loading="loading">
            <el-table-column prop="reportNo" label="报告编号" width="150" />
            <el-table-column prop="auditNo" label="审核编号" width="150" />
            <el-table-column prop="supplierName" label="供应商名称" min-width="200" />
            <el-table-column prop="type" label="审核类型" width="120">
              <template #default="scope">
                <el-tag :type="getAuditTypeColor(scope.row.type)">
                  {{ getAuditTypeText(scope.row.type) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="auditDate" label="审核日期" width="120" />
            <el-table-column prop="auditor" label="审核员" width="120" />
            <el-table-column prop="score" label="得分" width="80" />
            <el-table-column prop="conclusion" label="审核结论" width="120">
              <template #default="scope">
                <el-tag :type="getConclusionType(scope.row.conclusion)">
                  {{ scope.row.conclusion }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="200">
              <template #default="scope">
                <el-button type="primary" size="small" @click="handleViewReport(scope.row)">
                  查看
                </el-button>
                <el-button type="success" size="small" @click="handleDownloadReport(scope.row)">
                  下载
                </el-button>
                <el-button type="info" size="small" @click="handleShareReport(scope.row)">
                  分享
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
      </el-tabs>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'

export default {
  name: 'SupplierAudit',
  setup() {
    const router = useRouter()
    const loading = ref(false)
    const activeTab = ref('plan')

    // 统计数据
    const auditStats = reactive({
      total: 156,
      ongoing: 23,
      completed: 128,
      overdue: 5
    })

    // 搜索表单
    const planSearchForm = reactive({
      year: '2024',
      type: ''
    })

    const executionSearchForm = reactive({
      status: '',
      type: '',
      keyword: ''
    })

    const capaSearchForm = reactive({
      status: '',
      priority: '',
      keyword: ''
    })

    const reportSearchForm = reactive({
      dateRange: [],
      type: '',
      keyword: ''
    })

    // 分页信息
    const pagination = reactive({
      currentPage: 1,
      pageSize: 10,
      total: 0
    })

    // 数据列表
    const auditPlans = ref([])
    const auditExecutions = ref([])
    const capaList = ref([])
    const auditReports = ref([])

    // 初始化数据
    const initAuditPlans = () => {
      auditPlans.value = [
        {
          id: 1,
          planNo: 'AP2024001',
          year: '2024',
          type: 'annual',
          supplierCount: 45,
          plannedStart: '2024-01-01',
          plannedEnd: '2024-12-31',
          status: 'ongoing',
          progress: 65
        },
        {
          id: 2,
          planNo: 'AP2024002',
          year: '2024',
          type: 'monthly',
          supplierCount: 12,
          plannedStart: '2024-03-01',
          plannedEnd: '2024-03-31',
          status: 'completed',
          progress: 100
        }
      ]
    }

    const initAuditExecutions = () => {
      auditExecutions.value = [
        {
          id: 1,
          auditNo: 'AU2024001',
          supplierName: '博世汽车部件(苏州)有限公司',
          type: 'annual',
          auditor: '张工程师',
          plannedDate: '2024-03-15',
          actualDate: '2024-03-16',
          status: 'completed',
          score: 85,
          riskLevel: 'low',
          riskFactors: ['审核完成', '得分良好']
        },
        {
          id: 2,
          auditNo: 'AU2024002',
          supplierName: '大陆汽车系统(常熟)有限公司',
          type: 'monthly',
          auditor: '李工程师',
          plannedDate: '2024-03-20',
          actualDate: '',
          status: 'ongoing',
          score: 0,
          riskLevel: 'medium',
          riskFactors: ['审核进行中', '待评估']
        }
      ]
    }

    const initCAPAList = () => {
      capaList.value = [
        {
          id: 1,
          capaNo: 'CAPA2024001',
          supplierName: '法雷奥汽车空调湖北有限公司',
          auditNo: 'AU2024003',
          nonConformity: '质量管理体系文件不完整',
          priority: 'high',
          dueDate: '2024-04-15',
          status: 'ongoing'
        },
        {
          id: 2,
          capaNo: 'CAPA2024002',
          supplierName: '安波福电气系统有限公司',
          auditNo: 'AU2024004',
          nonConformity: '生产过程控制记录缺失',
          priority: 'medium',
          dueDate: '2024-04-20',
          status: 'verification'
        }
      ]
    }

    const initAuditReports = () => {
      auditReports.value = [
        {
          id: 1,
          reportNo: 'AR2024001',
          auditNo: 'AU2024001',
          supplierName: '博世汽车部件(苏州)有限公司',
          type: 'annual',
          auditDate: '2024-03-16',
          auditor: '张工程师',
          score: 85,
          conclusion: '合格'
        },
        {
          id: 2,
          reportNo: 'AR2024002',
          auditNo: 'AU2024005',
          supplierName: '法雷奥汽车空调湖北有限公司',
          type: 'special',
          auditDate: '2024-03-18',
          auditor: '王工程师',
          score: 72,
          conclusion: '有条件合格'
        }
      ]
    }

    // 工具方法
    const getAuditTypeColor = (type) => {
      const colors = {
        onboarding: 'primary',
        annual: 'success',
        monthly: 'info',
        special: 'warning',
        flying: 'danger'
      }
      return colors[type] || ''
    }

    const getAuditTypeText = (type) => {
      const texts = {
        onboarding: '准入审核',
        annual: '年度审核',
        monthly: '月度审核',
        special: '专项审核',
        flying: '飞行检查'
      }
      return texts[type] || type
    }

    const getPlanStatusType = (status) => {
      const types = {
        draft: 'info',
        ongoing: 'primary',
        completed: 'success',
        cancelled: 'danger'
      }
      return types[status] || ''
    }

    const getPlanStatusText = (status) => {
      const texts = {
        draft: '草稿',
        ongoing: '进行中',
        completed: '已完成',
        cancelled: '已取消'
      }
      return texts[status] || status
    }

    const getExecutionStatusType = (status) => {
      const types = {
        pending: 'info',
        ongoing: 'primary',
        completed: 'success',
        overdue: 'danger'
      }
      return types[status] || ''
    }

    const getExecutionStatusText = (status) => {
      const texts = {
        pending: '待开始',
        ongoing: '进行中',
        completed: '已完成',
        overdue: '已逾期'
      }
      return texts[status] || status
    }

    const getPriorityType = (priority) => {
      const types = {
        high: 'danger',
        medium: 'warning',
        low: 'info'
      }
      return types[priority] || ''
    }

    const getCAPAStatusType = (status) => {
      const types = {
        pending: 'info',
        ongoing: 'primary',
        verification: 'warning',
        closed: 'success',
        overdue: 'danger'
      }
      return types[status] || ''
    }

    const getCAPAStatusText = (status) => {
      const texts = {
        pending: '待处理',
        ongoing: '进行中',
        verification: '待验证',
        closed: '已关闭',
        overdue: '已逾期'
      }
      return texts[status] || status
    }

    const getConclusionType = (conclusion) => {
      const types = {
        '合格': 'success',
        '有条件合格': 'warning',
        '不合格': 'danger'
      }
      return types[conclusion] || ''
    }

    // 风险标记相关方法
    const getRiskLevelType = (riskLevel) => {
      const types = {
        low: 'success',
        medium: 'warning',
        high: 'danger',
        critical: 'danger'
      }
      return types[riskLevel] || 'info'
    }

    const getRiskLevelClass = (riskLevel) => {
      const classes = {
        low: 'risk-low',
        medium: 'risk-medium',
        high: 'risk-high',
        critical: 'risk-critical'
      }
      return classes[riskLevel] || ''
    }

    const getRiskLevelText = (riskLevel) => {
      const texts = {
        low: '低风险',
        medium: '中风险',
        high: '高风险',
        critical: '极高风险'
      }
      return texts[riskLevel] || riskLevel
    }

    const getRiskIcon = (riskLevel) => {
      const icons = {
        low: 'CircleCheck',
        medium: 'Warning',
        high: 'CircleClose',
        critical: 'WarnTriangleFilled'
      }
      return icons[riskLevel] || 'InfoFilled'
    }

    const getRiskFactorsText = (riskFactors) => {
      if (!riskFactors || riskFactors.length === 0) return ''
      return `风险因素：${riskFactors.join('、')}`
    }

    // 事件处理方法
    const handleTabChange = (tabName) => {
      activeTab.value = tabName
    }

    const handleCreateAudit = () => {
      router.push('/supplier-audit/create')
    }

    const handleExportReport = () => {
      console.log('导出审核报告')
    }

    const handleCreatePlan = () => {
      router.push('/supplier-audit/plan/create')
    }

    const handlePlanSearch = () => {
      console.log('搜索审核计划', planSearchForm)
    }

    const handleViewPlan = (row) => {
      router.push(`/supplier-audit/plan/${row.id}`)
    }

    const handleEditPlan = (row) => {
      router.push(`/supplier-audit/plan/edit/${row.id}`)
    }

    const handleExecutePlan = (row) => {
      console.log('执行审核计划', row)
    }

    const handleExecutionSearch = () => {
      console.log('搜索审核执行', executionSearchForm)
    }

    const handleStartAudit = (row) => {
      router.push(`/supplier-audit/execution/${row.id}`)
    }

    const handleViewReport = (row) => {
      router.push(`/supplier-audit/report/${row.id}`)
    }

    const handleViewCAPA = (row) => {
      router.push(`/supplier-audit/capa/${row.id}`)
    }

    const handleCAPASearch = () => {
      console.log('搜索CAPA', capaSearchForm)
    }

    const handleVerifyCAPA = (row) => {
      console.log('验证CAPA', row)
    }

    const handleCloseCAPA = (row) => {
      console.log('关闭CAPA', row)
    }

    const handleReportSearch = () => {
      console.log('搜索审核报告', reportSearchForm)
    }

    const handleDownloadReport = (row) => {
      console.log('下载报告', row)
    }

    const handleShareReport = (row) => {
      console.log('分享报告', row)
    }

    const handleSizeChange = (val) => {
      pagination.pageSize = val
    }

    const handleCurrentChange = (val) => {
      pagination.currentPage = val
    }

    onMounted(() => {
      initAuditPlans()
      initAuditExecutions()
      initCAPAList()
      initAuditReports()
    })

    return {
      loading,
      activeTab,
      auditStats,
      planSearchForm,
      executionSearchForm,
      capaSearchForm,
      reportSearchForm,
      pagination,
      auditPlans,
      auditExecutions,
      capaList,
      auditReports,
      getAuditTypeColor,
      getAuditTypeText,
      getPlanStatusType,
      getPlanStatusText,
      getExecutionStatusType,
      getExecutionStatusText,
      getPriorityType,
      getCAPAStatusType,
      getCAPAStatusText,
      getConclusionType,
      getRiskLevelType,
      getRiskLevelClass,
      getRiskLevelText,
      getRiskIcon,
      getRiskFactorsText,
      handleTabChange,
      handleCreateAudit,
      handleExportReport,
      handleCreatePlan,
      handlePlanSearch,
      handleViewPlan,
      handleEditPlan,
      handleExecutePlan,
      handleExecutionSearch,
      handleStartAudit,
      handleViewReport,
      handleViewCAPA,
      handleCAPASearch,
      handleVerifyCAPA,
      handleCloseCAPA,
      handleReportSearch,
      handleDownloadReport,
      handleShareReport,
      handleSizeChange,
      handleCurrentChange
    }
  }
}
</script>

<style scoped>
.supplier-audit-container {
  padding: 0;
}

.page-header {
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.page-header h2 {
  color: #303133;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.stats-row {
  margin-bottom: 20px;
}

.stats-card {
  position: relative;
  overflow: hidden;
}

.stats-content {
  position: relative;
  z-index: 2;
}

.stats-number {
  font-size: 32px;
  font-weight: bold;
  color: #303133;
  line-height: 1;
  margin-bottom: 8px;
}

.stats-label {
  font-size: 14px;
  color: #606266;
}

.stats-icon {
  position: absolute;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 40px;
  opacity: 0.3;
}

.stats-icon.total {
  color: #409EFF;
}

.stats-icon.ongoing {
  color: #E6A23C;
}

.stats-icon.completed {
  color: #67C23A;
}

.stats-icon.overdue {
  color: #F56C6C;
}

.main-card {
  min-height: 600px;
}

.tab-header {
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.filter-area {
  display: flex;
  gap: 10px;
  align-items: center;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

/* 表格样式优化 */
.el-table {
  border-radius: 8px;
  overflow: hidden;
}

.el-table .el-table__header {
  background-color: #f8f9fa;
}

.el-table .el-table__header th {
  background-color: #f8f9fa;
  color: #303133;
  font-weight: 600;
}

/* 标签样式 */
.el-tag {
  border-radius: 4px;
}

/* 进度条样式 */
.el-progress {
  width: 100%;
}

/* 按钮组样式 */
.el-button + .el-button {
  margin-left: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .stats-row .el-col {
    margin-bottom: 10px;
  }

  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }

  .tab-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }

  .filter-area {
    flex-wrap: wrap;
  }
}

/* 风险标记样式 */
.risk-indicator {
  display: flex;
  align-items: center;
  gap: 5px;
}

.risk-icon {
  margin-right: 2px;
}

.risk-info-icon {
  color: #909399;
  cursor: pointer;
  font-size: 14px;
}

.risk-info-icon:hover {
  color: #409EFF;
}

/* 风险等级特殊样式 */
.risk-low {
  background: linear-gradient(135deg, #67C23A, #85CE61);
  border: none;
  color: white;
  font-weight: 500;
}

.risk-medium {
  background: linear-gradient(135deg, #E6A23C, #EEBE77);
  border: none;
  color: white;
  font-weight: 500;
}

.risk-high {
  background: linear-gradient(135deg, #F56C6C, #F78989);
  border: none;
  color: white;
  font-weight: 500;
  animation: pulse-warning 2s infinite;
}

.risk-critical {
  background: linear-gradient(135deg, #F56C6C, #FF4444);
  border: none;
  color: white;
  font-weight: bold;
  animation: pulse-critical 1.5s infinite;
  box-shadow: 0 0 10px rgba(245, 108, 108, 0.5);
}

/* 风险警告动画 */
@keyframes pulse-warning {
  0% {
    box-shadow: 0 0 0 0 rgba(230, 162, 60, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(230, 162, 60, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(230, 162, 60, 0);
  }
}

@keyframes pulse-critical {
  0% {
    box-shadow: 0 0 0 0 rgba(245, 108, 108, 0.9);
  }
  70% {
    box-shadow: 0 0 0 15px rgba(245, 108, 108, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(245, 108, 108, 0);
  }
}
</style>
