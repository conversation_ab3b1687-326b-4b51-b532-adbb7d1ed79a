<template>
  <div class="supplier-performance-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>供应商绩效管理</h2>
      <p>对供应商绩效进行持续、量化和可视化的监控与评估，驱动供应商持续改进</p>
    </div>

    <!-- 绩效概览卡片 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-number">85</div>
            <div class="stats-label">平均绩效得分</div>
          </div>
          <el-icon class="stats-icon excellent"><Trophy /></el-icon>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-number">12</div>
            <div class="stats-label">优秀供应商</div>
          </div>
          <el-icon class="stats-icon good"><Star /></el-icon>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-number">5</div>
            <div class="stats-label">需改进供应商</div>
          </div>
          <el-icon class="stats-icon warning"><Warning /></el-icon>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-number">2</div>
            <div class="stats-label">改进计划中</div>
          </div>
          <el-icon class="stats-icon improving"><Tools /></el-icon>
        </el-card>
      </el-col>
    </el-row>

    <!-- 功能标签页 -->
    <el-card class="main-card">
      <el-tabs v-model="activeTab" @tab-change="handleTabChange">
        <!-- 绩效监控 -->
        <el-tab-pane label="绩效监控" name="monitoring">
          <div class="tab-header">
            <div class="filter-area">
              <el-select
                v-model="searchForm.period"
                placeholder="选择时间周期"
                style="width: 150px; margin-right: 10px"
              >
                <el-option label="本月" value="month" />
                <el-option label="本季度" value="quarter" />
                <el-option label="本年度" value="year" />
              </el-select>
              <el-select
                v-model="searchForm.category"
                placeholder="选择产品线"
                style="width: 150px; margin-right: 10px"
              >
                <el-option label="全部" value="" />
                <el-option label="制动系统" value="brake" />
                <el-option label="发动机" value="engine" />
                <el-option label="电子系统" value="electronic" />
              </el-select>
              <el-input
                v-model="searchForm.keyword"
                placeholder="请输入供应商名称"
                style="width: 200px; margin-right: 10px"
                clearable
              >
                <template #prefix>
                  <el-icon><Search /></el-icon>
                </template>
              </el-input>
              <el-button type="primary" @click="handleSearch">搜索</el-button>
              <el-button type="success" @click="handleExportReport">
                <el-icon><Download /></el-icon>
                导出报告
              </el-button>
            </div>
          </div>

          <el-table :data="performanceData" style="width: 100%" v-loading="loading">
            <el-table-column prop="supplierName" label="供应商名称" min-width="200" fixed="left" />
            <el-table-column prop="category" label="产品线" width="100" />
            <el-table-column prop="qualityScore" label="质量得分" width="100">
              <template #default="scope">
                <div class="score-cell">
                  <span :class="getScoreClass(scope.row.qualityScore)">{{ scope.row.qualityScore }}</span>
                  <el-progress
                    :percentage="scope.row.qualityScore"
                    :stroke-width="4"
                    :show-text="false"
                    :color="getScoreColor(scope.row.qualityScore)"
                  />
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="deliveryScore" label="交付得分" width="100">
              <template #default="scope">
                <div class="score-cell">
                  <span :class="getScoreClass(scope.row.deliveryScore)">{{ scope.row.deliveryScore }}</span>
                  <el-progress
                    :percentage="scope.row.deliveryScore"
                    :stroke-width="4"
                    :show-text="false"
                    :color="getScoreColor(scope.row.deliveryScore)"
                  />
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="costScore" label="成本得分" width="100">
              <template #default="scope">
                <div class="score-cell">
                  <span :class="getScoreClass(scope.row.costScore)">{{ scope.row.costScore }}</span>
                  <el-progress
                    :percentage="scope.row.costScore"
                    :stroke-width="4"
                    :show-text="false"
                    :color="getScoreColor(scope.row.costScore)"
                  />
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="serviceScore" label="服务得分" width="100">
              <template #default="scope">
                <div class="score-cell">
                  <span :class="getScoreClass(scope.row.serviceScore)">{{ scope.row.serviceScore }}</span>
                  <el-progress
                    :percentage="scope.row.serviceScore"
                    :stroke-width="4"
                    :show-text="false"
                    :color="getScoreColor(scope.row.serviceScore)"
                  />
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="totalScore" label="综合得分" width="120">
              <template #default="scope">
                <div class="total-score">
                  <span :class="getTotalScoreClass(scope.row.totalScore)">{{ scope.row.totalScore }}</span>
                  <el-tag :type="getPerformanceLevel(scope.row.totalScore).type" size="small">
                    {{ getPerformanceLevel(scope.row.totalScore).text }}
                  </el-tag>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="trend" label="趋势" width="80">
              <template #default="scope">
                <el-icon :class="getTrendClass(scope.row.trend)">
                  <component :is="getTrendIcon(scope.row.trend)" />
                </el-icon>
              </template>
            </el-table-column>
            <el-table-column prop="lastUpdate" label="更新时间" width="120" />
            <el-table-column label="操作" width="280" fixed="right">
              <template #default="scope">
                <div class="action-buttons">
                  <el-button type="primary" size="small" @click="handleViewDashboard(scope.row)">
                    仪表盘
                  </el-button>
                  <el-button type="success" size="small" @click="handleViewDetails(scope.row)">
                    详情
                  </el-button>
                  <el-button
                    type="warning"
                    size="small"
                    @click="handleCreateImprovement(scope.row)"
                    v-if="scope.row.totalScore < 70"
                  >
                    改进计划
                  </el-button>
                  <el-dropdown @command="handleCommand">
                    <el-button type="primary" size="small">
                      更多<el-icon class="el-icon--right"><arrow-down /></el-icon>
                    </el-button>
                    <template #dropdown>
                      <el-dropdown-menu>
                        <el-dropdown-item :command="{action: 'history', row: scope.row}">
                          历史记录
                        </el-dropdown-item>
                        <el-dropdown-item :command="{action: 'compare', row: scope.row}">
                          对比分析
                        </el-dropdown-item>
                        <el-dropdown-item :command="{action: 'alert', row: scope.row}" divided>
                          设置预警
                        </el-dropdown-item>
                      </el-dropdown-menu>
                    </template>
                  </el-dropdown>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>

        <!-- KPI配置 -->
        <el-tab-pane label="KPI配置" name="kpi">
          <div class="tab-header">
            <el-button type="primary" @click="handleAddKPI">
              <el-icon><Plus /></el-icon>
              新增KPI
            </el-button>
            <el-button type="success" @click="handleConfigWeight">
              <el-icon><Setting /></el-icon>
              权重配置
            </el-button>
          </div>

          <el-table :data="kpiData" style="width: 100%">
            <el-table-column prop="category" label="指标类别" width="120" />
            <el-table-column prop="name" label="指标名称" min-width="200" />
            <el-table-column prop="unit" label="单位" width="80" />
            <el-table-column prop="weight" label="权重" width="80">
              <template #default="scope">
                {{ scope.row.weight }}%
              </template>
            </el-table-column>
            <el-table-column prop="target" label="目标值" width="100" />
            <el-table-column prop="dataSource" label="数据源" width="120" />
            <el-table-column prop="frequency" label="更新频率" width="100" />
            <el-table-column prop="status" label="状态" width="100">
              <template #default="scope">
                <el-tag :type="scope.row.status === 'active' ? 'success' : 'info'">
                  {{ scope.row.status === 'active' ? '启用' : '禁用' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="200">
              <template #default="scope">
                <el-button type="primary" size="small" @click="handleEditKPI(scope.row)">
                  编辑
                </el-button>
                <el-button type="warning" size="small" @click="handleToggleKPI(scope.row)">
                  {{ scope.row.status === 'active' ? '禁用' : '启用' }}
                </el-button>
                <el-button type="danger" size="small" @click="handleDeleteKPI(scope.row)">
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>

        <!-- 改进计划 -->
        <el-tab-pane label="改进计划" name="improvement">
          <div class="tab-header">
            <el-button type="primary" @click="handleNewImprovement">
              <el-icon><Plus /></el-icon>
              新增改进计划
            </el-button>
            <div class="filter-area">
              <el-select
                v-model="improvementSearchForm.status"
                placeholder="计划状态"
                style="width: 150px; margin-left: 10px"
                clearable
              >
                <el-option label="全部" value="" />
                <el-option label="进行中" value="ongoing" />
                <el-option label="已完成" value="completed" />
                <el-option label="已逾期" value="overdue" />
              </el-select>
              <el-button type="primary" @click="handleImprovementSearch">搜索</el-button>
            </div>
          </div>

          <el-table :data="improvementData" style="width: 100%">
            <el-table-column prop="planNo" label="计划编号" width="140" />
            <el-table-column prop="supplierName" label="供应商名称" min-width="200" />
            <el-table-column prop="problemArea" label="问题领域" width="120" />
            <el-table-column prop="targetScore" label="目标得分" width="100" />
            <el-table-column prop="currentScore" label="当前得分" width="100" />
            <el-table-column prop="responsible" label="负责人" width="100" />
            <el-table-column prop="deadline" label="截止时间" width="120" />
            <el-table-column prop="status" label="状态" width="100">
              <template #default="scope">
                <el-tag :type="getImprovementStatusType(scope.row.status)">
                  {{ getImprovementStatusText(scope.row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="progress" label="进度" width="120">
              <template #default="scope">
                <el-progress :percentage="scope.row.progress" :stroke-width="6" />
              </template>
            </el-table-column>
            <el-table-column label="操作" width="200">
              <template #default="scope">
                <el-button type="primary" size="small" @click="handleViewImprovement(scope.row)">
                  查看
                </el-button>
                <el-button type="success" size="small" @click="handleEditImprovement(scope.row)">
                  编辑
                </el-button>
                <el-button type="warning" size="small" @click="handleCloseImprovement(scope.row)">
                  关闭
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
      </el-tabs>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'

export default {
  name: 'SupplierPerformance',
  setup() {
    const router = useRouter()
    const loading = ref(false)
    const activeTab = ref('monitoring')

    // 搜索表单
    const searchForm = reactive({
      period: 'quarter',
      category: '',
      keyword: ''
    })

    const improvementSearchForm = reactive({
      status: ''
    })

    // 分页信息
    const pagination = reactive({
      currentPage: 1,
      pageSize: 10,
      total: 0
    })

    // 绩效数据
    const performanceData = ref([])
    
    // KPI数据
    const kpiData = ref([])
    
    // 改进计划数据
    const improvementData = ref([])

    // 初始化数据
    const initPerformanceData = () => {
      performanceData.value = [
        {
          id: 1,
          supplierName: '博世汽车部件(苏州)有限公司',
          category: '制动系统',
          qualityScore: 92,
          deliveryScore: 88,
          costScore: 85,
          serviceScore: 90,
          totalScore: 89,
          trend: 'up',
          lastUpdate: '2024-03-01'
        },
        {
          id: 2,
          supplierName: '大陆汽车系统(常熟)有限公司',
          category: '电子系统',
          qualityScore: 85,
          deliveryScore: 92,
          costScore: 78,
          serviceScore: 88,
          totalScore: 86,
          trend: 'stable',
          lastUpdate: '2024-03-01'
        },
        {
          id: 3,
          supplierName: '法雷奥汽车空调湖北有限公司',
          category: '发动机',
          qualityScore: 75,
          deliveryScore: 70,
          costScore: 82,
          serviceScore: 76,
          totalScore: 75,
          trend: 'down',
          lastUpdate: '2024-03-01'
        },
        {
          id: 4,
          supplierName: '麦格纳汽车技术(上海)有限公司',
          category: '制动系统',
          qualityScore: 95,
          deliveryScore: 94,
          costScore: 88,
          serviceScore: 92,
          totalScore: 92,
          trend: 'up',
          lastUpdate: '2024-03-01'
        },
        {
          id: 5,
          supplierName: '安波福电气系统有限公司',
          category: '电子系统',
          qualityScore: 65,
          deliveryScore: 68,
          costScore: 70,
          serviceScore: 65,
          totalScore: 67,
          trend: 'down',
          lastUpdate: '2024-03-01'
        }
      ]
      pagination.total = performanceData.value.length
    }

    const initKPIData = () => {
      kpiData.value = [
        {
          id: 1,
          category: '质量指标',
          name: '来料不良率(PPM)',
          unit: 'PPM',
          weight: 25,
          target: '<100',
          dataSource: 'MES系统',
          frequency: '每日',
          status: 'active'
        },
        {
          id: 2,
          category: '质量指标',
          name: '批次合格率',
          unit: '%',
          weight: 25,
          target: '>99%',
          dataSource: 'MES系统',
          frequency: '每日',
          status: 'active'
        },
        {
          id: 3,
          category: '交付指标',
          name: '交期达成率(OTD)',
          unit: '%',
          weight: 20,
          target: '>95%',
          dataSource: 'ERP系统',
          frequency: '每日',
          status: 'active'
        },
        {
          id: 4,
          category: '交付指标',
          name: '订单完成率',
          unit: '%',
          weight: 10,
          target: '>98%',
          dataSource: 'ERP系统',
          frequency: '每周',
          status: 'active'
        },
        {
          id: 5,
          category: '成本指标',
          name: '年度降价达成率',
          unit: '%',
          weight: 15,
          target: '>3%',
          dataSource: '手工录入',
          frequency: '每季度',
          status: 'active'
        },
        {
          id: 6,
          category: '服务指标',
          name: '技术支持及时性',
          unit: '小时',
          weight: 5,
          target: '<24',
          dataSource: '手工录入',
          frequency: '每月',
          status: 'active'
        }
      ]
    }

    const initImprovementData = () => {
      improvementData.value = [
        {
          id: 1,
          planNo: 'IMP2024001',
          supplierName: '法雷奥汽车空调湖北有限公司',
          problemArea: '交付质量',
          targetScore: 85,
          currentScore: 75,
          responsible: '采购部-李经理',
          deadline: '2024-06-30',
          status: 'ongoing',
          progress: 60
        },
        {
          id: 2,
          planNo: 'IMP2024002',
          supplierName: '安波福电气系统有限公司',
          problemArea: '综合绩效',
          targetScore: 80,
          currentScore: 67,
          responsible: '质量部-张工程师',
          deadline: '2024-05-31',
          status: 'ongoing',
          progress: 40
        },
        {
          id: 3,
          planNo: 'IMP2023015',
          supplierName: '某改进完成供应商',
          problemArea: '成本控制',
          targetScore: 85,
          currentScore: 88,
          responsible: '采购部-王主管',
          deadline: '2024-02-28',
          status: 'completed',
          progress: 100
        }
      ]
    }

    // 评分相关方法
    const getScoreClass = (score) => {
      if (score >= 90) return 'score-excellent'
      if (score >= 80) return 'score-good'
      if (score >= 70) return 'score-average'
      return 'score-poor'
    }

    const getScoreColor = (score) => {
      if (score >= 90) return '#67C23A'
      if (score >= 80) return '#409EFF'
      if (score >= 70) return '#E6A23C'
      return '#F56C6C'
    }

    const getTotalScoreClass = (score) => {
      if (score >= 90) return 'total-score-excellent'
      if (score >= 80) return 'total-score-good'
      if (score >= 70) return 'total-score-average'
      return 'total-score-poor'
    }

    const getPerformanceLevel = (score) => {
      if (score >= 90) return { type: 'success', text: 'A级' }
      if (score >= 80) return { type: 'primary', text: 'B级' }
      if (score >= 70) return { type: 'warning', text: 'C级' }
      return { type: 'danger', text: 'D级' }
    }

    const getTrendClass = (trend) => {
      const classes = {
        up: 'trend-up',
        down: 'trend-down',
        stable: 'trend-stable'
      }
      return classes[trend] || ''
    }

    const getTrendIcon = (trend) => {
      const icons = {
        up: 'TrendCharts',
        down: 'Bottom',
        stable: 'Minus'
      }
      return icons[trend] || 'Minus'
    }

    const getImprovementStatusType = (status) => {
      const types = {
        ongoing: 'warning',
        completed: 'success',
        overdue: 'danger',
        cancelled: 'info'
      }
      return types[status] || ''
    }

    const getImprovementStatusText = (status) => {
      const texts = {
        ongoing: '进行中',
        completed: '已完成',
        overdue: '已逾期',
        cancelled: '已取消'
      }
      return texts[status] || status
    }

    // 事件处理方法
    const handleTabChange = (tabName) => {
      activeTab.value = tabName
    }

    const handleSearch = () => {
      console.log('搜索绩效数据', searchForm)
    }

    const handleExportReport = () => {
      console.log('导出绩效报告')
    }

    const handleViewDashboard = (row) => {
      router.push(`/supplier-performance/dashboard/${row.id}`)
    }

    const handleViewDetails = (row) => {
      console.log('查看详情', row)
    }

    const handleCreateImprovement = (row) => {
      router.push(`/supplier-performance/improvement/${row.id}`)
    }

    const handleCommand = (command) => {
      console.log('执行操作', command)
    }

    const handleAddKPI = () => {
      console.log('新增KPI')
    }

    const handleConfigWeight = () => {
      console.log('权重配置')
    }

    const handleEditKPI = (row) => {
      console.log('编辑KPI', row)
    }

    const handleToggleKPI = (row) => {
      row.status = row.status === 'active' ? 'inactive' : 'active'
    }

    const handleDeleteKPI = (row) => {
      console.log('删除KPI', row)
    }

    const handleNewImprovement = () => {
      console.log('新增改进计划')
    }

    const handleImprovementSearch = () => {
      console.log('搜索改进计划', improvementSearchForm)
    }

    const handleViewImprovement = (row) => {
      router.push(`/supplier-performance/improvement/${row.id}`)
    }

    const handleEditImprovement = (row) => {
      console.log('编辑改进计划', row)
    }

    const handleCloseImprovement = (row) => {
      console.log('关闭改进计划', row)
    }

    const handleSizeChange = (val) => {
      pagination.pageSize = val
    }

    const handleCurrentChange = (val) => {
      pagination.currentPage = val
    }

    onMounted(() => {
      initPerformanceData()
      initKPIData()
      initImprovementData()
    })

    return {
      loading,
      activeTab,
      searchForm,
      improvementSearchForm,
      pagination,
      performanceData,
      kpiData,
      improvementData,
      getScoreClass,
      getScoreColor,
      getTotalScoreClass,
      getPerformanceLevel,
      getTrendClass,
      getTrendIcon,
      getImprovementStatusType,
      getImprovementStatusText,
      handleTabChange,
      handleSearch,
      handleExportReport,
      handleViewDashboard,
      handleViewDetails,
      handleCreateImprovement,
      handleCommand,
      handleAddKPI,
      handleConfigWeight,
      handleEditKPI,
      handleToggleKPI,
      handleDeleteKPI,
      handleNewImprovement,
      handleImprovementSearch,
      handleViewImprovement,
      handleEditImprovement,
      handleCloseImprovement,
      handleSizeChange,
      handleCurrentChange
    }
  }
}
</script>

<style scoped>
.supplier-performance-container {
  padding: 0;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  color: #303133;
  margin-bottom: 8px;
}

.page-header p {
  color: #909399;
  font-size: 14px;
}

.stats-row {
  margin-bottom: 20px;
}

.stats-card {
  position: relative;
  overflow: hidden;
}

.stats-content {
  padding: 10px 0;
}

.stats-number {
  font-size: 28px;
  font-weight: bold;
  color: #303133;
  line-height: 1;
}

.stats-label {
  font-size: 14px;
  color: #909399;
  margin-top: 8px;
}

.stats-icon {
  position: absolute;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 32px;
  opacity: 0.3;
}

.stats-icon.excellent {
  color: #67C23A;
}

.stats-icon.good {
  color: #409EFF;
}

.stats-icon.warning {
  color: #E6A23C;
}

.stats-icon.improving {
  color: #909399;
}

.main-card {
  margin-bottom: 20px;
}

.tab-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.filter-area {
  display: flex;
  align-items: center;
}

.score-cell {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.score-cell span {
  font-weight: bold;
  font-size: 12px;
}

.score-excellent {
  color: #67C23A;
}

.score-good {
  color: #409EFF;
}

.score-average {
  color: #E6A23C;
}

.score-poor {
  color: #F56C6C;
}

.total-score {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.total-score span {
  font-weight: bold;
  font-size: 16px;
}

.total-score-excellent {
  color: #67C23A;
}

.total-score-good {
  color: #409EFF;
}

.total-score-average {
  color: #E6A23C;
}

.total-score-poor {
  color: #F56C6C;
}

.trend-up {
  color: #67C23A;
  font-size: 18px;
}

.trend-down {
  color: #F56C6C;
  font-size: 18px;
}

.trend-stable {
  color: #909399;
  font-size: 18px;
}

.action-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  align-items: center;
}

.action-buttons .el-button {
  margin: 0;
  min-width: auto;
  padding: 5px 8px;
  font-size: 12px;
}

.action-buttons .el-dropdown {
  margin-left: 4px;
}

.pagination-container {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .action-buttons .el-button {
    padding: 4px 6px;
    font-size: 11px;
  }

  .stats-row .el-col {
    margin-bottom: 10px;
  }
}

@media (max-width: 768px) {
  .tab-header {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }

  .filter-area {
    flex-direction: column;
    gap: 10px;
  }

  .filter-area .el-input,
  .filter-area .el-select {
    width: 100% !important;
  }
}
</style>
